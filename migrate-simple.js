const Airtable = require("airtable");
const axios = require("axios");
const { createClient } = require('@supabase/supabase-js');
const sharp = require('sharp');
const path = require('path');

// --- Configuration ---
const AIRTABLE_API_KEY = '**********************************************************************************';
const AIRTABLE_BASE_ID = 'appYfr5h5YVzQO8GZ';
const AIRTABLE_TABLE_NAME = 'Data';

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

// Storage buckets
const LOGO_BUCKET = 'company-logos';
const PDF_BUCKET = 'case-study-pdfs';

// --- Clients ---
const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(AIRTABLE_BASE_ID);
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Clean filename for storage
 */
function cleanFilename(filename, company = '', type = '') {
  const clean = filename
    .replace(/[^a-zA-Z0-9.-]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase();
  
  if (company && type === 'pdf') {
    const companyClean = company.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    const timestamp = Date.now();
    return `${companyClean}-${clean}-${timestamp}.pdf`;
  } else if (company && type === 'logo') {
    const companyClean = company.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    const ext = path.extname(clean) || '.png';
    return `${companyClean}-logo${ext}`;
  }
  
  return clean;
}

/**
 * Compress image if needed
 */
async function compressImage(buffer, filename) {
  try {
    const ext = path.extname(filename).toLowerCase();
    
    if (['.jpg', '.jpeg', '.png', '.webp'].includes(ext)) {
      console.log(`🗜️  Compressing image: ${filename}`);
      
      const compressed = await sharp(buffer)
        .resize(400, 300, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .jpeg({ 
          quality: 80,
          progressive: true 
        })
        .toBuffer();
      
      const originalSize = buffer.length;
      const compressedSize = compressed.length;
      const savings = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
      
      console.log(`📊 Compression: ${(originalSize/1024).toFixed(1)}KB → ${(compressedSize/1024).toFixed(1)}KB (${savings}% saved)`);
      
      return compressed;
    }
    
    return buffer;
  } catch (error) {
    console.log(`⚠️  Could not compress ${filename}, using original`);
    return buffer;
  }
}

/**
 * Upload file to Supabase storage
 */
async function uploadToSupabase(filename, fileBuffer, contentType, bucket, company = '', type = '') {
  try {
    const cleanName = cleanFilename(filename, company, type);
    
    // Compress if it's an image
    let processedBuffer = fileBuffer;
    if (type === 'logo') {
      processedBuffer = await compressImage(fileBuffer, filename);
    }
    
    const uploadURL = `${SUPABASE_URL}/storage/v1/object/${bucket}/${cleanName}`;
    const response = await axios.put(
      uploadURL,
      processedBuffer,
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': contentType,
        },
      }
    );
    
    if (response.status === 200) {
      const publicURL = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${cleanName}`;
      console.log(`✅ Uploaded: ${cleanName} (${(processedBuffer.length/1024).toFixed(1)}KB)`);
      return publicURL;
    }
    return null;
  } catch (error) {
    console.error(`❌ Upload failed for ${filename}:`, error.response?.data || error.message);
    return null;
  }
}

/**
 * Process attachment and upload to appropriate bucket
 */
async function processAttachment(attachments, company, type) {
  if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
    return null;
  }
  
  const attachment = attachments[0];
  const { url, filename, type: contentType } = attachment;
  
  try {
    console.log(`📥 Downloading ${type}: ${filename}`);
    const downloadResponse = await axios.get(url, { responseType: 'arraybuffer' });
    
    const bucket = type === 'logo' ? LOGO_BUCKET : PDF_BUCKET;
    const uploadedUrl = await uploadToSupabase(
      filename, 
      downloadResponse.data, 
      contentType, 
      bucket, 
      company, 
      type
    );
    
    return uploadedUrl || url; // Fallback to original URL if upload fails
  } catch (error) {
    console.error(`❌ Error processing ${type} ${filename}:`, error.message);
    return url; // Fallback to original URL
  }
}

/**
 * Insert record into existing airtable_data table
 */
async function insertRecord(record) {
  const fields = record.fields;
  
  // Process attachments
  const logoUrl = await processAttachment(fields.Logo, fields.Company, 'logo');
  const pdfUrl = await processAttachment(fields.PDF, fields.Company, 'pdf');
  
  // Map fields to match existing table structure
  const data = {
    airtable_id: record.id,
    name: fields.Name || null,
    type_field: fields.Type ? JSON.stringify(fields.Type) : null,
    organizer: fields.Organizer || null,
    company: fields.Company || null,
    logo: fields.Logo ? JSON.stringify(fields.Logo) : null,
    logo_url: logoUrl,
    objective: fields.Objective ? JSON.stringify(fields.Objective) : null,
    category: fields.Category ? JSON.stringify(fields.Category) : null,
    image_tags_extra: fields['Image Tags Extra'] ? JSON.stringify(fields['Image Tags Extra']) : null,
    publish: fields.Publish || null,
    sort_field: fields.Sort || null,
    likes: fields.Likes || null,
    new_image_tag: fields['New Image Tag'] ? JSON.stringify(fields['New Image Tag']) : null,
    likes_filter: fields['Likes Filter'] ? JSON.stringify(fields['Likes Filter']) : null,
    likes_filter_formula: fields['Likes Filter Formula'] || null,
    creators_tag: fields['Creators Tag'] || null,
    market: fields.Market || null,
    pdf: fields.PDF ? JSON.stringify(fields.PDF) : null,
    pdf_url: pdfUrl
  };
  
  try {
    const { data: insertedData, error } = await supabase
      .from('airtable_data')
      .insert([data]);
    
    if (error) {
      console.error('❌ Error inserting record:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error inserting record:', error.message);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateData() {
  console.log('🚀 Starting optimized Airtable to Supabase migration...\n');
  console.log('📁 Files will be organized as:');
  console.log(`   • Company logos → ${LOGO_BUCKET} bucket`);
  console.log(`   • Case study PDFs → ${PDF_BUCKET} bucket`);
  console.log('');
  
  let totalRecords = 0;
  let successfulRecords = 0;
  let logoCount = 0;
  let pdfCount = 0;
  
  try {
    await new Promise((resolve, reject) => {
      base(AIRTABLE_TABLE_NAME).select({
        view: 'Grid view'
      }).eachPage(async (records, fetchNextPage) => {
        console.log(`📄 Processing ${records.length} records...`);
        
        for (const record of records) {
          totalRecords++;
          
          try {
            const success = await insertRecord(record);
            if (success) {
              successfulRecords++;
              
              // Count processed files
              if (record.fields.Logo) logoCount++;
              if (record.fields.PDF) pdfCount++;
            }
            
            if (totalRecords % 10 === 0) {
              console.log(`📈 Progress: ${successfulRecords}/${totalRecords} records migrated`);
            }
            
          } catch (error) {
            console.error(`❌ Error processing record ${record.id}:`, error.message);
          }
        }
        
        fetchNextPage();
      }, (err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
    
    console.log(`\n🎉 Migration complete!`);
    console.log(`📊 Results:`);
    console.log(`   • ${successfulRecords}/${totalRecords} case studies migrated`);
    console.log(`   • ${logoCount} company logos processed`);
    console.log(`   • ${pdfCount} PDF files processed`);
    console.log(`   • Files organized in separate buckets with clean naming`);
    console.log(`   • Images compressed for better performance`);
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
  }
}

// Run the migration
migrateData().catch(err => {
  console.error("💥 Critical error:", err);
});
