import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace meet_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Google Meet API
     *
     * Create and manage meetings in Google Meet.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const meet = google.meet('v2');
     * ```
     */
    export class Meet {
        context: APIRequestContext;
        conferenceRecords: Resource$Conferencerecords;
        spaces: Resource$Spaces;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Active conference.
     */
    export interface Schema$ActiveConference {
        /**
         * Output only. Reference to 'ConferenceRecord' resource. Format: `conferenceRecords/{conference_record\}` where `{conference_record\}` is a unique ID for each instance of a call within a space.
         */
        conferenceRecord?: string | null;
    }
    /**
     * User who joins anonymously (meaning not signed into a Google Account).
     */
    export interface Schema$AnonymousUser {
        /**
         * Output only. User provided name when they join a conference anonymously.
         */
        displayName?: string | null;
    }
    /**
     * Configuration related to meeting artifacts potentially generated by this meeting space.
     */
    export interface Schema$ArtifactConfig {
        /**
         * Configuration for recording.
         */
        recordingConfig?: Schema$RecordingConfig;
        /**
         * Configuration for auto-smart-notes.
         */
        smartNotesConfig?: Schema$SmartNotesConfig;
        /**
         * Configuration for auto-transcript.
         */
        transcriptionConfig?: Schema$TranscriptionConfig;
    }
    /**
     * Single instance of a meeting held in a space.
     */
    export interface Schema$ConferenceRecord {
        /**
         * Output only. Timestamp when the conference ended. Set for past conferences. Unset if the conference is ongoing.
         */
        endTime?: string | null;
        /**
         * Output only. Server enforced expiration time for when this conference record resource is deleted. The resource is deleted 30 days after the conference ends.
         */
        expireTime?: string | null;
        /**
         * Identifier. Resource name of the conference record. Format: `conferenceRecords/{conference_record\}` where `{conference_record\}` is a unique ID for each instance of a call within a space.
         */
        name?: string | null;
        /**
         * Output only. The space where the conference was held.
         */
        space?: string | null;
        /**
         * Output only. Timestamp when the conference started. Always set.
         */
        startTime?: string | null;
    }
    /**
     * Google Docs location where the transcript file is saved.
     */
    export interface Schema$DocsDestination {
        /**
         * Output only. The document ID for the underlying Google Docs transcript file. For example, "1kuceFZohVoCh6FulBHxwy6I15Ogpc4hP". Use the `documents.get` method of the Google Docs API (https://developers.google.com/docs/api/reference/rest/v1/documents/get) to fetch the content.
         */
        document?: string | null;
        /**
         * Output only. URI for the Google Docs transcript file. Use `https://docs.google.com/document/d/{$DocumentId\}/view` to browse the transcript in the browser.
         */
        exportUri?: string | null;
    }
    /**
     * Export location where a recording file is saved in Google Drive.
     */
    export interface Schema$DriveDestination {
        /**
         * Output only. Link used to play back the recording file in the browser. For example, `https://drive.google.com/file/d/{$fileId\}/view`.
         */
        exportUri?: string | null;
        /**
         * Output only. The `fileId` for the underlying MP4 file. For example, "1kuceFZohVoCh6FulBHxwy6I15Ogpc4hP". Use `$ GET https://www.googleapis.com/drive/v3/files/{$fileId\}?alt=media` to download the blob. For more information, see https://developers.google.com/drive/api/v3/reference/files/get.
         */
        file?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Request to end an ongoing conference of a space.
     */
    export interface Schema$EndActiveConferenceRequest {
    }
    /**
     * Response of ListConferenceRecords method.
     */
    export interface Schema$ListConferenceRecordsResponse {
        /**
         * List of conferences in one page.
         */
        conferenceRecords?: Schema$ConferenceRecord[];
        /**
         * Token to be circulated back for further List call if current List does NOT include all the Conferences. Unset if all conferences have been returned.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response of ListParticipants method.
     */
    export interface Schema$ListParticipantSessionsResponse {
        /**
         * Token to be circulated back for further List call if current List doesn't include all the participants. Unset if all participants are returned.
         */
        nextPageToken?: string | null;
        /**
         * List of participants in one page.
         */
        participantSessions?: Schema$ParticipantSession[];
    }
    /**
     * Response of ListParticipants method.
     */
    export interface Schema$ListParticipantsResponse {
        /**
         * Token to be circulated back for further List call if current List doesn't include all the participants. Unset if all participants are returned.
         */
        nextPageToken?: string | null;
        /**
         * List of participants in one page.
         */
        participants?: Schema$Participant[];
        /**
         * Total, exact number of `participants`. By default, this field isn't included in the response. Set the field mask in [SystemParameterContext](https://cloud.google.com/apis/docs/system-parameters) to receive this field in the response.
         */
        totalSize?: number | null;
    }
    /**
     * Response for ListRecordings method.
     */
    export interface Schema$ListRecordingsResponse {
        /**
         * Token to be circulated back for further List call if current List doesn't include all the recordings. Unset if all recordings are returned.
         */
        nextPageToken?: string | null;
        /**
         * List of recordings in one page.
         */
        recordings?: Schema$Recording[];
    }
    /**
     * Response for ListTranscriptEntries method.
     */
    export interface Schema$ListTranscriptEntriesResponse {
        /**
         * Token to be circulated back for further List call if current List doesn't include all the transcript entries. Unset if all entries are returned.
         */
        nextPageToken?: string | null;
        /**
         * List of TranscriptEntries in one page.
         */
        transcriptEntries?: Schema$TranscriptEntry[];
    }
    /**
     * Response for ListTranscripts method.
     */
    export interface Schema$ListTranscriptsResponse {
        /**
         * Token to be circulated back for further List call if current List doesn't include all the transcripts. Unset if all transcripts are returned.
         */
        nextPageToken?: string | null;
        /**
         * List of transcripts in one page.
         */
        transcripts?: Schema$Transcript[];
    }
    /**
     * Defines restrictions for features when the meeting is moderated.
     */
    export interface Schema$ModerationRestrictions {
        /**
         * Defines who has permission to send chat messages in the meeting space.
         */
        chatRestriction?: string | null;
        /**
         * Defines whether to restrict the default role assigned to users as viewer.
         */
        defaultJoinAsViewerType?: string | null;
        /**
         * Defines who has permission to share their screen in the meeting space.
         */
        presentRestriction?: string | null;
        /**
         * Defines who has permission to send reactions in the meeting space.
         */
        reactionRestriction?: string | null;
    }
    /**
     * User who attended or is attending a conference.
     */
    export interface Schema$Participant {
        /**
         * Anonymous user.
         */
        anonymousUser?: Schema$AnonymousUser;
        /**
         * Output only. Time when the participant first joined the meeting.
         */
        earliestStartTime?: string | null;
        /**
         * Output only. Time when the participant left the meeting for the last time. This can be null if it's an active meeting.
         */
        latestEndTime?: string | null;
        /**
         * Output only. Resource name of the participant. Format: `conferenceRecords/{conference_record\}/participants/{participant\}`
         */
        name?: string | null;
        /**
         * User calling from their phone.
         */
        phoneUser?: Schema$PhoneUser;
        /**
         * Signed-in user.
         */
        signedinUser?: Schema$SignedinUser;
    }
    /**
     * Refers to each unique join or leave session when a user joins a conference from a device. Note that any time a user joins the conference a new unique ID is assigned. That means if a user joins a space multiple times from the same device, they're assigned different IDs, and are also be treated as different participant sessions.
     */
    export interface Schema$ParticipantSession {
        /**
         * Output only. Timestamp when the user session ends. Unset if the user session hasn’t ended.
         */
        endTime?: string | null;
        /**
         * Identifier. Session id.
         */
        name?: string | null;
        /**
         * Output only. Timestamp when the user session starts.
         */
        startTime?: string | null;
    }
    /**
     * User dialing in from a phone where the user's identity is unknown because they haven't signed in with a Google Account.
     */
    export interface Schema$PhoneUser {
        /**
         * Output only. Partially redacted user's phone number when calling.
         */
        displayName?: string | null;
    }
    /**
     * Metadata about a recording created during a conference.
     */
    export interface Schema$Recording {
        /**
         * Output only. Recording is saved to Google Drive as an MP4 file. The `drive_destination` includes the Drive `fileId` that can be used to download the file using the `files.get` method of the Drive API.
         */
        driveDestination?: Schema$DriveDestination;
        /**
         * Output only. Timestamp when the recording ended.
         */
        endTime?: string | null;
        /**
         * Output only. Resource name of the recording. Format: `conferenceRecords/{conference_record\}/recordings/{recording\}` where `{recording\}` is a 1:1 mapping to each unique recording session during the conference.
         */
        name?: string | null;
        /**
         * Output only. Timestamp when the recording started.
         */
        startTime?: string | null;
        /**
         * Output only. Current state.
         */
        state?: string | null;
    }
    /**
     * Configuration related to recording in a meeting space.
     */
    export interface Schema$RecordingConfig {
        /**
         * Defines whether a meeting space is automatically recorded when someone with the privilege to record joins the meeting.
         */
        autoRecordingGeneration?: string | null;
    }
    /**
     * A signed-in user can be: a) An individual joining from a personal computer, mobile device, or through companion mode. b) A robot account used by conference room devices.
     */
    export interface Schema$SignedinUser {
        /**
         * Output only. For a personal device, it's the user's first name and last name. For a robot account, it's the administrator-specified device name. For example, "Altostrat Room".
         */
        displayName?: string | null;
        /**
         * Output only. Unique ID for the user. Interoperable with Admin SDK API and People API. Format: `users/{user\}`
         */
        user?: string | null;
    }
    /**
     * Configuration related to smart notes in a meeting space. For more information about smart notes, see ["Take notes for me" in Google Meet](https://support.google.com/meet/answer/********).
     */
    export interface Schema$SmartNotesConfig {
        /**
         * Defines whether to automatically generate a summary and recap of the meeting for all invitees in the organization when someone with the privilege to enable smart notes joins the meeting.
         */
        autoSmartNotesGeneration?: string | null;
    }
    /**
     * Virtual place where conferences are held. Only one active conference can be held in one space at any given time.
     */
    export interface Schema$Space {
        /**
         * Active conference, if it exists.
         */
        activeConference?: Schema$ActiveConference;
        /**
         * Configuration pertaining to the meeting space.
         */
        config?: Schema$SpaceConfig;
        /**
         * Output only. Type friendly unique string used to join the meeting. Format: `[a-z]+-[a-z]+-[a-z]+`. For example, `abc-mnop-xyz`. The maximum length is 128 characters. Can only be used as an alias of the space name to get the space.
         */
        meetingCode?: string | null;
        /**
         * Output only. URI used to join meetings consisting of `https://meet.google.com/` followed by the `meeting_code`. For example, `https://meet.google.com/abc-mnop-xyz`.
         */
        meetingUri?: string | null;
        /**
         * Immutable. Resource name of the space. Format: `spaces/{space\}`. `{space\}` is the resource identifier for the space. It's a unique, server-generated ID and is case sensitive. For example, `jQCFfuBOdN5z`. For more information, see [How Meet identifies a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#identify-meeting-space).
         */
        name?: string | null;
    }
    /**
     * The configuration pertaining to a meeting space.
     */
    export interface Schema$SpaceConfig {
        /**
         * Access type of the meeting space that determines who can join without knocking. Default: The user's default access settings. Controlled by the user's admin for enterprise users or RESTRICTED.
         */
        accessType?: string | null;
        /**
         * Configuration pertaining to the auto-generated artifacts that the meeting supports.
         */
        artifactConfig?: Schema$ArtifactConfig;
        /**
         * Whether attendance report is enabled for the meeting space.
         */
        attendanceReportGenerationType?: string | null;
        /**
         * Defines the entry points that can be used to join meetings hosted in this meeting space. Default: EntryPointAccess.ALL
         */
        entryPointAccess?: string | null;
        /**
         * The pre-configured moderation mode for the Meeting. Default: Controlled by the user's policies.
         */
        moderation?: string | null;
        /**
         * When moderation.ON, these restrictions go into effect for the meeting. When moderation.OFF, will be reset to default ModerationRestrictions.
         */
        moderationRestrictions?: Schema$ModerationRestrictions;
    }
    /**
     * Metadata for a transcript generated from a conference. It refers to the ASR (Automatic Speech Recognition) result of user's speech during the conference.
     */
    export interface Schema$Transcript {
        /**
         * Output only. Where the Google Docs transcript is saved.
         */
        docsDestination?: Schema$DocsDestination;
        /**
         * Output only. Timestamp when the transcript stopped.
         */
        endTime?: string | null;
        /**
         * Output only. Resource name of the transcript. Format: `conferenceRecords/{conference_record\}/transcripts/{transcript\}`, where `{transcript\}` is a 1:1 mapping to each unique transcription session of the conference.
         */
        name?: string | null;
        /**
         * Output only. Timestamp when the transcript started.
         */
        startTime?: string | null;
        /**
         * Output only. Current state.
         */
        state?: string | null;
    }
    /**
     * Single entry for one user’s speech during a transcript session.
     */
    export interface Schema$TranscriptEntry {
        /**
         * Output only. Timestamp when the transcript entry ended.
         */
        endTime?: string | null;
        /**
         * Output only. Language of spoken text, such as "en-US". IETF BCP 47 syntax (https://tools.ietf.org/html/bcp47)
         */
        languageCode?: string | null;
        /**
         * Output only. Resource name of the entry. Format: "conferenceRecords/{conference_record\}/transcripts/{transcript\}/entries/{entry\}"
         */
        name?: string | null;
        /**
         * Output only. Refers to the participant who speaks.
         */
        participant?: string | null;
        /**
         * Output only. Timestamp when the transcript entry started.
         */
        startTime?: string | null;
        /**
         * Output only. The transcribed text of the participant's voice, at maximum 10K words. Note that the limit is subject to change.
         */
        text?: string | null;
    }
    /**
     * Configuration related to transcription in a meeting space.
     */
    export interface Schema$TranscriptionConfig {
        /**
         * Defines whether the content of a meeting is automatically transcribed when someone with the privilege to transcribe joins the meeting.
         */
        autoTranscriptionGeneration?: string | null;
    }
    export class Resource$Conferencerecords {
        context: APIRequestContext;
        participants: Resource$Conferencerecords$Participants;
        recordings: Resource$Conferencerecords$Recordings;
        transcripts: Resource$Conferencerecords$Transcripts;
        constructor(context: APIRequestContext);
        /**
         * Gets a conference record by conference ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ConferenceRecord>>;
        get(params: Params$Resource$Conferencerecords$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Get, options: MethodOptions | BodyResponseCallback<Schema$ConferenceRecord>, callback: BodyResponseCallback<Schema$ConferenceRecord>): void;
        get(params: Params$Resource$Conferencerecords$Get, callback: BodyResponseCallback<Schema$ConferenceRecord>): void;
        get(callback: BodyResponseCallback<Schema$ConferenceRecord>): void;
        /**
         * Lists the conference records. By default, ordered by start time and in descending order.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListConferenceRecordsResponse>>;
        list(params: Params$Resource$Conferencerecords$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$List, options: MethodOptions | BodyResponseCallback<Schema$ListConferenceRecordsResponse>, callback: BodyResponseCallback<Schema$ListConferenceRecordsResponse>): void;
        list(params: Params$Resource$Conferencerecords$List, callback: BodyResponseCallback<Schema$ListConferenceRecordsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListConferenceRecordsResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Get extends StandardParameters {
        /**
         * Required. Resource name of the conference.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$List extends StandardParameters {
        /**
         * Optional. User specified filtering condition in [EBNF format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form). The following are the filterable fields: * `space.meeting_code` * `space.name` * `start_time` * `end_time` For example, consider the following filters: * `space.name = "spaces/NAME"` * `space.meeting_code = "abc-mnop-xyz"` * `start_time\>="2024-01-01T00:00:00.000Z" AND start_time<="2024-01-02T00:00:00.000Z"` * `end_time IS NULL`
         */
        filter?: string;
        /**
         * Optional. Maximum number of conference records to return. The service might return fewer than this value. If unspecified, at most 25 conference records are returned. The maximum value is 100; values above 100 are coerced to 100. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Optional. Page token returned from previous List Call.
         */
        pageToken?: string;
    }
    export class Resource$Conferencerecords$Participants {
        context: APIRequestContext;
        participantSessions: Resource$Conferencerecords$Participants$Participantsessions;
        constructor(context: APIRequestContext);
        /**
         * Gets a participant by participant ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Participants$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Participants$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Participant>>;
        get(params: Params$Resource$Conferencerecords$Participants$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Participants$Get, options: MethodOptions | BodyResponseCallback<Schema$Participant>, callback: BodyResponseCallback<Schema$Participant>): void;
        get(params: Params$Resource$Conferencerecords$Participants$Get, callback: BodyResponseCallback<Schema$Participant>): void;
        get(callback: BodyResponseCallback<Schema$Participant>): void;
        /**
         * Lists the participants in a conference record. By default, ordered by join time and in descending order. This API supports `fields` as standard parameters like every other API. However, when the `fields` request parameter is omitted, this API defaults to `'participants/x, next_page_token'`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$Participants$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$Participants$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListParticipantsResponse>>;
        list(params: Params$Resource$Conferencerecords$Participants$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$Participants$List, options: MethodOptions | BodyResponseCallback<Schema$ListParticipantsResponse>, callback: BodyResponseCallback<Schema$ListParticipantsResponse>): void;
        list(params: Params$Resource$Conferencerecords$Participants$List, callback: BodyResponseCallback<Schema$ListParticipantsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListParticipantsResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Participants$Get extends StandardParameters {
        /**
         * Required. Resource name of the participant.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$Participants$List extends StandardParameters {
        /**
         * Optional. User specified filtering condition in [EBNF format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form). The following are the filterable fields: * `earliest_start_time` * `latest_end_time` For example, `latest_end_time IS NULL` returns active participants in the conference.
         */
        filter?: string;
        /**
         * Maximum number of participants to return. The service might return fewer than this value. If unspecified, at most 100 participants are returned. The maximum value is 250; values above 250 are coerced to 250. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Page token returned from previous List Call.
         */
        pageToken?: string;
        /**
         * Required. Format: `conferenceRecords/{conference_record\}`
         */
        parent?: string;
    }
    export class Resource$Conferencerecords$Participants$Participantsessions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets a participant session by participant session ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Participants$Participantsessions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Participants$Participantsessions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ParticipantSession>>;
        get(params: Params$Resource$Conferencerecords$Participants$Participantsessions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Participants$Participantsessions$Get, options: MethodOptions | BodyResponseCallback<Schema$ParticipantSession>, callback: BodyResponseCallback<Schema$ParticipantSession>): void;
        get(params: Params$Resource$Conferencerecords$Participants$Participantsessions$Get, callback: BodyResponseCallback<Schema$ParticipantSession>): void;
        get(callback: BodyResponseCallback<Schema$ParticipantSession>): void;
        /**
         * Lists the participant sessions of a participant in a conference record. By default, ordered by join time and in descending order. This API supports `fields` as standard parameters like every other API. However, when the `fields` request parameter is omitted this API defaults to `'participantsessions/x, next_page_token'`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$Participants$Participantsessions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$Participants$Participantsessions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListParticipantSessionsResponse>>;
        list(params: Params$Resource$Conferencerecords$Participants$Participantsessions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$Participants$Participantsessions$List, options: MethodOptions | BodyResponseCallback<Schema$ListParticipantSessionsResponse>, callback: BodyResponseCallback<Schema$ListParticipantSessionsResponse>): void;
        list(params: Params$Resource$Conferencerecords$Participants$Participantsessions$List, callback: BodyResponseCallback<Schema$ListParticipantSessionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListParticipantSessionsResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Participants$Participantsessions$Get extends StandardParameters {
        /**
         * Required. Resource name of the participant.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$Participants$Participantsessions$List extends StandardParameters {
        /**
         * Optional. User specified filtering condition in [EBNF format](https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form). The following are the filterable fields: * `start_time` * `end_time` For example, `end_time IS NULL` returns active participant sessions in the conference record.
         */
        filter?: string;
        /**
         * Optional. Maximum number of participant sessions to return. The service might return fewer than this value. If unspecified, at most 100 participants are returned. The maximum value is 250; values above 250 are coerced to 250. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Optional. Page token returned from previous List Call.
         */
        pageToken?: string;
        /**
         * Required. Format: `conferenceRecords/{conference_record\}/participants/{participant\}`
         */
        parent?: string;
    }
    export class Resource$Conferencerecords$Recordings {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets a recording by recording ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Recordings$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Recordings$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Recording>>;
        get(params: Params$Resource$Conferencerecords$Recordings$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Recordings$Get, options: MethodOptions | BodyResponseCallback<Schema$Recording>, callback: BodyResponseCallback<Schema$Recording>): void;
        get(params: Params$Resource$Conferencerecords$Recordings$Get, callback: BodyResponseCallback<Schema$Recording>): void;
        get(callback: BodyResponseCallback<Schema$Recording>): void;
        /**
         * Lists the recording resources from the conference record. By default, ordered by start time and in ascending order.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$Recordings$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$Recordings$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListRecordingsResponse>>;
        list(params: Params$Resource$Conferencerecords$Recordings$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$Recordings$List, options: MethodOptions | BodyResponseCallback<Schema$ListRecordingsResponse>, callback: BodyResponseCallback<Schema$ListRecordingsResponse>): void;
        list(params: Params$Resource$Conferencerecords$Recordings$List, callback: BodyResponseCallback<Schema$ListRecordingsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRecordingsResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Recordings$Get extends StandardParameters {
        /**
         * Required. Resource name of the recording.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$Recordings$List extends StandardParameters {
        /**
         * Maximum number of recordings to return. The service might return fewer than this value. If unspecified, at most 10 recordings are returned. The maximum value is 100; values above 100 are coerced to 100. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Page token returned from previous List Call.
         */
        pageToken?: string;
        /**
         * Required. Format: `conferenceRecords/{conference_record\}`
         */
        parent?: string;
    }
    export class Resource$Conferencerecords$Transcripts {
        context: APIRequestContext;
        entries: Resource$Conferencerecords$Transcripts$Entries;
        constructor(context: APIRequestContext);
        /**
         * Gets a transcript by transcript ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Transcripts$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Transcripts$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Transcript>>;
        get(params: Params$Resource$Conferencerecords$Transcripts$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Transcripts$Get, options: MethodOptions | BodyResponseCallback<Schema$Transcript>, callback: BodyResponseCallback<Schema$Transcript>): void;
        get(params: Params$Resource$Conferencerecords$Transcripts$Get, callback: BodyResponseCallback<Schema$Transcript>): void;
        get(callback: BodyResponseCallback<Schema$Transcript>): void;
        /**
         * Lists the set of transcripts from the conference record. By default, ordered by start time and in ascending order.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$Transcripts$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$Transcripts$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListTranscriptsResponse>>;
        list(params: Params$Resource$Conferencerecords$Transcripts$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$Transcripts$List, options: MethodOptions | BodyResponseCallback<Schema$ListTranscriptsResponse>, callback: BodyResponseCallback<Schema$ListTranscriptsResponse>): void;
        list(params: Params$Resource$Conferencerecords$Transcripts$List, callback: BodyResponseCallback<Schema$ListTranscriptsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTranscriptsResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Transcripts$Get extends StandardParameters {
        /**
         * Required. Resource name of the transcript.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$Transcripts$List extends StandardParameters {
        /**
         * Maximum number of transcripts to return. The service might return fewer than this value. If unspecified, at most 10 transcripts are returned. The maximum value is 100; values above 100 are coerced to 100. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Page token returned from previous List Call.
         */
        pageToken?: string;
        /**
         * Required. Format: `conferenceRecords/{conference_record\}`
         */
        parent?: string;
    }
    export class Resource$Conferencerecords$Transcripts$Entries {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets a `TranscriptEntry` resource by entry ID. Note: The transcript entries returned by the Google Meet API might not match the transcription found in the Google Docs transcript file. This can occur when 1) we have interleaved speakers within milliseconds, or 2) the Google Docs transcript file is modified after generation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Conferencerecords$Transcripts$Entries$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Conferencerecords$Transcripts$Entries$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TranscriptEntry>>;
        get(params: Params$Resource$Conferencerecords$Transcripts$Entries$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Conferencerecords$Transcripts$Entries$Get, options: MethodOptions | BodyResponseCallback<Schema$TranscriptEntry>, callback: BodyResponseCallback<Schema$TranscriptEntry>): void;
        get(params: Params$Resource$Conferencerecords$Transcripts$Entries$Get, callback: BodyResponseCallback<Schema$TranscriptEntry>): void;
        get(callback: BodyResponseCallback<Schema$TranscriptEntry>): void;
        /**
         * Lists the structured transcript entries per transcript. By default, ordered by start time and in ascending order. Note: The transcript entries returned by the Google Meet API might not match the transcription found in the Google Docs transcript file. This can occur when 1) we have interleaved speakers within milliseconds, or 2) the Google Docs transcript file is modified after generation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Conferencerecords$Transcripts$Entries$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Conferencerecords$Transcripts$Entries$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListTranscriptEntriesResponse>>;
        list(params: Params$Resource$Conferencerecords$Transcripts$Entries$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Conferencerecords$Transcripts$Entries$List, options: MethodOptions | BodyResponseCallback<Schema$ListTranscriptEntriesResponse>, callback: BodyResponseCallback<Schema$ListTranscriptEntriesResponse>): void;
        list(params: Params$Resource$Conferencerecords$Transcripts$Entries$List, callback: BodyResponseCallback<Schema$ListTranscriptEntriesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTranscriptEntriesResponse>): void;
    }
    export interface Params$Resource$Conferencerecords$Transcripts$Entries$Get extends StandardParameters {
        /**
         * Required. Resource name of the `TranscriptEntry`.
         */
        name?: string;
    }
    export interface Params$Resource$Conferencerecords$Transcripts$Entries$List extends StandardParameters {
        /**
         * Maximum number of entries to return. The service might return fewer than this value. If unspecified, at most 10 entries are returned. The maximum value is 100; values above 100 are coerced to 100. Maximum might change in the future.
         */
        pageSize?: number;
        /**
         * Page token returned from previous List Call.
         */
        pageToken?: string;
        /**
         * Required. Format: `conferenceRecords/{conference_record\}/transcripts/{transcript\}`
         */
        parent?: string;
    }
    export class Resource$Spaces {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a space.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Spaces$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Spaces$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Space>>;
        create(params: Params$Resource$Spaces$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Spaces$Create, options: MethodOptions | BodyResponseCallback<Schema$Space>, callback: BodyResponseCallback<Schema$Space>): void;
        create(params: Params$Resource$Spaces$Create, callback: BodyResponseCallback<Schema$Space>): void;
        create(callback: BodyResponseCallback<Schema$Space>): void;
        /**
         * Ends an active conference (if there's one). For an example, see [End active conference](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#end-active-conference).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        endActiveConference(params: Params$Resource$Spaces$Endactiveconference, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        endActiveConference(params?: Params$Resource$Spaces$Endactiveconference, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        endActiveConference(params: Params$Resource$Spaces$Endactiveconference, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        endActiveConference(params: Params$Resource$Spaces$Endactiveconference, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        endActiveConference(params: Params$Resource$Spaces$Endactiveconference, callback: BodyResponseCallback<Schema$Empty>): void;
        endActiveConference(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets details about a meeting space. For an example, see [Get a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#get-meeting-space).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Spaces$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Spaces$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Space>>;
        get(params: Params$Resource$Spaces$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Spaces$Get, options: MethodOptions | BodyResponseCallback<Schema$Space>, callback: BodyResponseCallback<Schema$Space>): void;
        get(params: Params$Resource$Spaces$Get, callback: BodyResponseCallback<Schema$Space>): void;
        get(callback: BodyResponseCallback<Schema$Space>): void;
        /**
         * Updates details about a meeting space. For an example, see [Update a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#update-meeting-space).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Spaces$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Spaces$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Space>>;
        patch(params: Params$Resource$Spaces$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Spaces$Patch, options: MethodOptions | BodyResponseCallback<Schema$Space>, callback: BodyResponseCallback<Schema$Space>): void;
        patch(params: Params$Resource$Spaces$Patch, callback: BodyResponseCallback<Schema$Space>): void;
        patch(callback: BodyResponseCallback<Schema$Space>): void;
    }
    export interface Params$Resource$Spaces$Create extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Space;
    }
    export interface Params$Resource$Spaces$Endactiveconference extends StandardParameters {
        /**
         * Required. Resource name of the space. Format: `spaces/{space\}`. `{space\}` is the resource identifier for the space. It's a unique, server-generated ID and is case sensitive. For example, `jQCFfuBOdN5z`. For more information, see [How Meet identifies a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#identify-meeting-space).
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$EndActiveConferenceRequest;
    }
    export interface Params$Resource$Spaces$Get extends StandardParameters {
        /**
         * Required. Resource name of the space. Format: `spaces/{space\}` or `spaces/{meetingCode\}`. `{space\}` is the resource identifier for the space. It's a unique, server-generated ID and is case sensitive. For example, `jQCFfuBOdN5z`. `{meetingCode\}` is an alias for the space. It's a typeable, unique character string and is non-case sensitive. For example, `abc-mnop-xyz`. The maximum length is 128 characters. A `meetingCode` shouldn't be stored long term as it can become dissociated from a meeting space and can be reused for different meeting spaces in the future. Generally, a `meetingCode` expires 365 days after last use. For more information, see [Learn about meeting codes in Google Meet](https://support.google.com/meet/answer/10710509). For more information, see [How Meet identifies a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#identify-meeting-space).
         */
        name?: string;
    }
    export interface Params$Resource$Spaces$Patch extends StandardParameters {
        /**
         * Immutable. Resource name of the space. Format: `spaces/{space\}`. `{space\}` is the resource identifier for the space. It's a unique, server-generated ID and is case sensitive. For example, `jQCFfuBOdN5z`. For more information, see [How Meet identifies a meeting space](https://developers.google.com/workspace/meet/api/guides/meeting-spaces#identify-meeting-space).
         */
        name?: string;
        /**
         * Optional. Field mask used to specify the fields to be updated in the space. If update_mask isn't provided(not set, set with empty paths, or only has "" as paths), it defaults to update all fields provided with values in the request. Using "*" as update_mask will update all fields, including deleting fields not set in the request.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Space;
    }
    export {};
}
