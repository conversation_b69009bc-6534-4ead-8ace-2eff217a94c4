const { createClient } = require('@supabase/supabase-js');

// Supabase Configuration
const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function createTable() {
  console.log('🏗️  Creating Supabase table...');
  
  // Simple table creation SQL
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS airtable_data (
      id SERIAL PRIMARY KEY,
      name TEXT,
      type_field JSONB,
      organizer TEXT,
      company TEXT,
      logo TEXT,
      logo_url TEXT,
      objective JSONB,
      category JSONB,
      image_tags_extra JSONB,
      publish TEXT,
      sort_field NUMERIC,
      likes NUMER<PERSON>,
      new_image_tag JSONB,
      likes_filter JSONB,
      likes_filter_formula TEXT,
      creators_tag TEXT,
      market TEXT,
      pdf TEXT,
      pdf_url TEXT,
      airtable_id TEXT UNIQUE,
      created_at TIMESTAMP DEFAULT NOW(),
      updated_at TIMESTAMP DEFAULT NOW()
    );
  `;
  
  console.log('📝 SQL to execute:');
  console.log(createTableSQL);
  
  try {
    // Method 1: Try using raw SQL query
    const { data, error } = await supabase
      .from('_sql')
      .select('*')
      .eq('query', createTableSQL);
    
    if (error) {
      console.log('❌ Method 1 failed:', error.message);
      
      // Method 2: Try using rpc
      console.log('🔄 Trying method 2...');
      const { data: rpcData, error: rpcError } = await supabase.rpc('exec_sql', {
        sql: createTableSQL
      });
      
      if (rpcError) {
        console.log('❌ Method 2 failed:', rpcError.message);
        
        // Method 3: Manual approach - check if we can at least connect
        console.log('🔄 Trying method 3 - testing connection...');
        const { data: testData, error: testError } = await supabase
          .from('airtable_data')
          .select('id')
          .limit(1);
        
        if (testError && testError.code === 'PGRST116') {
          console.log('✅ Table doesn\'t exist - this is expected');
          console.log('📋 Please create the table manually in Supabase dashboard:');
          console.log('1. Go to https://supabase.com/dashboard/project/rnpxnaqfoqdivxrlozfr');
          console.log('2. Click on "SQL Editor" in the sidebar');
          console.log('3. Paste and run this SQL:');
          console.log('\n' + createTableSQL);
          return false;
        } else if (!testError) {
          console.log('✅ Table already exists!');
          return true;
        } else {
          console.log('❌ Unexpected error:', testError);
          return false;
        }
      } else {
        console.log('✅ Table created successfully with method 2!');
        return true;
      }
    } else {
      console.log('✅ Table created successfully with method 1!');
      return true;
    }
  } catch (error) {
    console.error('❌ Error creating table:', error.message);
    console.log('\n📋 Please create the table manually in Supabase dashboard:');
    console.log('1. Go to https://supabase.com/dashboard/project/rnpxnaqfoqdivxrlozfr');
    console.log('2. Click on "SQL Editor" in the sidebar');
    console.log('3. Paste and run this SQL:');
    console.log('\n' + createTableSQL);
    return false;
  }
}

// Test the connection and table creation
async function testConnection() {
  console.log('🔌 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('_health')
      .select('*')
      .limit(1);
    
    console.log('✅ Supabase connection successful!');
  } catch (error) {
    console.log('⚠️  Connection test inconclusive, but this is normal');
  }
  
  // Try to create the table
  const tableCreated = await createTable();
  
  if (tableCreated) {
    console.log('🎉 Ready to proceed with migration!');
  } else {
    console.log('⚠️  Please create the table manually before running migration');
  }
}

testConnection().catch(err => {
  console.error("💥 Critical error:", err);
});
