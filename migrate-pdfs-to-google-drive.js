const { createClient } = require('@supabase/supabase-js');
const { google } = require('googleapis');
const fs = require('fs');
const https = require('https');
require('dotenv').config();

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

class PDFToGoogleDriveMigration {
  constructor() {
    this.oauth2Client = null;
    this.drive = null;
    this.folderId = '1E_IxXxD2_klvg5USwC-ICerHGRTrYfMe';
    this.stats = {
      totalRecords: 0,
      processed: 0,
      uploaded: 0,
      skipped: 0,
      errors: 0,
      totalSize: 0
    };
  }

  /**
   * Initialize OAuth2 authentication
   */
  async initializeAuth() {
    try {
      // Check for OAuth2 credentials
      if (!fs.existsSync('./oauth2-credentials.json')) {
        console.log('❌ OAuth2 credentials not found!');
        console.log('📋 Please run: node google-drive-oauth2-setup.js');
        return false;
      }

      // Check for authentication token
      if (!fs.existsSync('./google-drive-token.json')) {
        console.log('❌ Authentication token not found!');
        console.log('📋 Please run: node google-drive-oauth2-setup.js');
        return false;
      }

      // Load credentials and token
      const credentials = JSON.parse(fs.readFileSync('./oauth2-credentials.json', 'utf8'));
      const token = JSON.parse(fs.readFileSync('./google-drive-token.json', 'utf8'));

      const { client_secret, client_id, redirect_uris } = credentials.installed || credentials.web;

      this.oauth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris?.[0] || 'http://localhost:3000/oauth2callback'
      );

      this.oauth2Client.setCredentials(token);
      this.drive = google.drive({ version: 'v3', auth: this.oauth2Client });

      console.log('✅ OAuth2 authentication initialized');
      return true;
    } catch (error) {
      console.error('❌ Auth initialization failed:', error.message);
      console.log('📋 Please run: node google-drive-oauth2-setup.js');
      return false;
    }
  }

  /**
   * Download file from URL
   */
  async downloadFile(url) {
    return new Promise((resolve, reject) => {
      const timeout = 30000; // 30 seconds timeout
      
      const request = https.get(url, { timeout }, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        const chunks = [];
        let totalSize = 0;
        
        response.on('data', (chunk) => {
          chunks.push(chunk);
          totalSize += chunk.length;
          
          // Limit file size to 100MB
          if (totalSize > 100 * 1024 * 1024) {
            response.destroy();
            reject(new Error('File too large (>100MB)'));
            return;
          }
        });
        
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      });
      
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Download timeout'));
      });
      
      request.on('error', reject);
    });
  }

  /**
   * Upload file to Google Drive
   */
  async uploadToGoogleDrive(fileName, fileBuffer) {
    try {
      const fileMetadata = {
        name: fileName,
        parents: [this.folderId]
      };

      const media = {
        mimeType: 'application/pdf',
        body: require('stream').Readable.from(fileBuffer)
      };

      console.log(`   📤 Uploading ${this.formatBytes(fileBuffer.length)}...`);
      
      const response = await this.drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id,name,webViewLink,webContentLink'
      });

      // Make file publicly viewable
      await this.drive.permissions.create({
        fileId: response.data.id,
        resource: {
          role: 'reader',
          type: 'anyone'
        }
      });

      return {
        id: response.data.id,
        name: response.data.name,
        viewLink: response.data.webViewLink,
        downloadLink: response.data.webContentLink,
        size: fileBuffer.length
      };
    } catch (error) {
      console.error(`   ❌ Upload failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate clean filename
   */
  generateFileName(company, originalName, timestamp) {
    const cleanCompany = company
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 30); // Limit length

    const cleanOriginal = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50); // Limit length

    return `${cleanCompany}-${cleanOriginal}-${timestamp}.pdf`;
  }

  /**
   * Format bytes
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Migrate all records
   */
  async migrate() {
    console.log('🚀 PDF Migration to Google Drive');
    console.log('================================');
    console.log(`📁 Target folder: https://drive.google.com/drive/folders/${this.folderId}`);
    console.log('');

    // Initialize authentication
    const authReady = await this.initializeAuth();
    if (!authReady) {
      return;
    }

    try {
      // Get all records with Airtable URLs
      console.log('📊 Fetching records from database...');
      const { data: records, error } = await supabase
        .from('airtable_data')
        .select('id, name, company, pdf_url, pdf, created_at')
        .not('pdf', 'is', null)
        .like('pdf_url', '%airtableusercontent.com%') // Only Airtable URLs
        .order('created_at', { ascending: true });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      this.stats.totalRecords = records.length;
      console.log(`📋 Found ${records.length} records with Airtable URLs to migrate`);
      
      if (records.length === 0) {
        console.log('');
        console.log('✅ No records need migration - all PDFs already on Google Drive!');
        return;
      }
      
      console.log('');
      console.log('🔄 Starting migration...');
      console.log('⚠️  This will take some time for 800+ files');
      console.log('');

      for (const record of records) {
        await this.migrateRecord(record);

        // Progress update every 10 records
        if (this.stats.processed % 10 === 0) {
          console.log('');
          console.log(`📈 Progress: ${this.stats.processed}/${this.stats.totalRecords} (${Math.round(this.stats.processed/this.stats.totalRecords*100)}%)`);
          console.log(`   ✅ Uploaded: ${this.stats.uploaded}`);
          console.log(`   ⏭️  Skipped: ${this.stats.skipped}`);
          console.log(`   ❌ Errors: ${this.stats.errors}`);
          console.log(`   💾 Total size: ${this.formatBytes(this.stats.totalSize)}`);
          console.log('');
        }

        // Small delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Final results
      console.log('');
      console.log('🎉 MIGRATION COMPLETE!');
      console.log('======================');
      console.log(`📊 Total records: ${this.stats.totalRecords}`);
      console.log(`✅ Successfully uploaded: ${this.stats.uploaded}`);
      console.log(`⏭️  Skipped: ${this.stats.skipped}`);
      console.log(`❌ Errors: ${this.stats.errors}`);
      console.log(`💾 Total uploaded: ${this.formatBytes(this.stats.totalSize)}`);
      console.log('');
      console.log('🔗 All PDFs are now:');
      console.log('   • Stored in your Google Drive folder');
      console.log('   • Publicly accessible via Google Drive URLs');
      console.log('   • Updated in your Supabase database');
      console.log('   • Named with proper conventions');
      console.log('');
      console.log(`📁 View all files: https://drive.google.com/drive/folders/${this.folderId}`);

    } catch (error) {
      console.error('💥 Migration failed:', error.message);
    }
  }

  /**
   * Migrate single record
   */
  async migrateRecord(record) {
    try {
      this.stats.processed++;
      
      console.log(`📄 ${this.stats.processed}/${this.stats.totalRecords}: ${record.name} (${record.company})`);
      
      // Parse PDF data to get Airtable URL
      let pdfData;
      try {
        pdfData = JSON.parse(record.pdf);
      } catch (parseError) {
        throw new Error(`Could not parse PDF data`);
      }

      if (!pdfData || pdfData.length === 0 || !pdfData[0].url) {
        throw new Error('No valid PDF URL found');
      }

      const airtableUrl = pdfData[0].url;
      const originalFilename = pdfData[0].filename || 'document.pdf';
      
      // Generate Google Drive filename
      const timestamp = new Date(record.created_at).getTime();
      const driveFilename = this.generateFileName(record.company, originalFilename, timestamp);
      
      console.log(`   📥 Downloading from Airtable...`);
      const fileBuffer = await this.downloadFile(airtableUrl);
      
      console.log(`   📤 Uploading to Google Drive: ${driveFilename}`);
      const uploadResult = await this.uploadToGoogleDrive(driveFilename, fileBuffer);
      
      // Update database with Google Drive URL
      console.log(`   💾 Updating database...`);
      const { error: updateError } = await supabase
        .from('airtable_data')
        .update({ pdf_url: uploadResult.viewLink })
        .eq('id', record.id);
      
      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`);
      }
      
      this.stats.uploaded++;
      this.stats.totalSize += uploadResult.size;
      
      console.log(`   ✅ Success: ${uploadResult.viewLink}`);
      
    } catch (error) {
      this.stats.errors++;
      console.error(`   ❌ Failed: ${error.message}`);
      
      // Continue with next record instead of stopping
      if (error.message.includes('timeout') || error.message.includes('ENOTFOUND')) {
        console.log(`   ⏭️  Skipping due to network issue, will retry later`);
      }
    }
  }
}

// Run migration
if (require.main === module) {
  const migration = new PDFToGoogleDriveMigration();
  migration.migrate().catch(err => {
    console.error('💥 Critical error:', err.message);
    process.exit(1);
  });
}

module.exports = PDFToGoogleDriveMigration;
