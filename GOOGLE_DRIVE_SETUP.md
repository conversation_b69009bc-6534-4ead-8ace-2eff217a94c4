# 🚀 Google Drive Setup Guide

## Quick Setup (5 minutes)

### 1. Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a new project** (or select existing)
3. **Enable Google Drive API:**
   - Go to "APIs & Services" → "Library"
   - Search "Google Drive API"
   - Click "Enable"

### 2. Create Service Account

1. **Go to "APIs & Services" → "Credentials"**
2. **Click "Create Credentials" → "Service Account"**
3. **Fill details:**
   - Name: `case-study-uploader`
   - Description: `Upload case study PDFs to Google Drive`
4. **Click "Create and Continue"**
5. **Skip role assignment** (click "Continue")
6. **Click "Done"**

### 3. Download Credentials

1. **Click on the created service account**
2. **Go to "Keys" tab**
3. **Click "Add Key" → "Create new key"**
4. **Select "JSON" format**
5. **Download the file**
6. **Rename to `google-drive-credentials.json`**
7. **Place in your project root folder**

### 4. Create Google Drive Folder

1. **Go to [Google Drive](https://drive.google.com)**
2. **Create new folder:** "Case Study PDFs"
3. **Right-click folder → "Share"**
4. **Add the service account email** (from credentials JSON file)
5. **Set permission to "Editor"**
6. **Copy the folder ID** from URL: `https://drive.google.com/drive/folders/FOLDER_ID_HERE`

### 5. Configure Environment

1. **Copy `.env.example` to `.env`**
2. **Edit `.env` file:**
   ```
   GOOGLE_DRIVE_FOLDER_ID=your_actual_folder_id_here
   GOOGLE_DRIVE_CREDENTIALS_PATH=./google-drive-credentials.json
   ```

### 6. Test Setup

```bash
node test-google-drive.js
```

If successful, you'll see:
```
✅ Google Drive API initialized successfully
✅ Folder accessible: 0 files, 0 Bytes
✅ Test file uploaded successfully
```

### 7. Run Migration

```bash
node migrate-from-airtable-to-drive.js
```

This will:
- ✅ Upload all 800 PDFs from Airtable to Google Drive
- ✅ Update database with Google Drive links
- ✅ Maintain all data integrity
- ✅ Keep Supabase storage under 1GB limit

## 🔒 Security Notes

- ✅ Service account only has access to the shared folder
- ✅ Credentials file is local only (add to .gitignore)
- ✅ Google Drive files are private by default
- ✅ Can be made publicly viewable if needed

## 📊 Expected Results

After migration:
- **800 PDFs** stored in organized Google Drive folder
- **Database updated** with Google Drive view links
- **Supabase storage**: ~9MB (only logos)
- **All case studies** accessible via Google Drive links

## 🆘 Troubleshooting

**"Failed to initialize Google Drive API"**
- Check credentials file path and format
- Verify service account email in folder sharing

**"Folder not accessible"**
- Verify folder ID is correct
- Check service account has Editor permission

**"Upload failed"**
- Check internet connection
- Verify Airtable URLs are still accessible
