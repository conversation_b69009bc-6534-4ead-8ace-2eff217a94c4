const Airtable = require("airtable");
const axios = require("axios");
const { createClient } = require('@supabase/supabase-js');
const sharp = require('sharp'); // For image compression
const path = require('path');

// --- Configuration ---
const AIRTABLE_API_KEY = '**********************************************************************************';
const AIRTABLE_BASE_ID = 'appYfr5h5YVzQO8GZ';
const AIRTABLE_TABLE_NAME = 'Data';

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

// Separate buckets for better organization
const LOGO_BUCKET = 'company-logos';
const PDF_BUCKET = 'case-study-pdfs';
const MAIN_TABLE = 'airtable_data';
const COMPANIES_TABLE = 'companies';

// --- Clients ---
const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(AIRTABLE_BASE_ID);
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Clean filename for storage
 */
function cleanFilename(filename, company = '', type = '') {
  // Remove special characters and spaces
  const clean = filename
    .replace(/[^a-zA-Z0-9.-]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase();
  
  if (company && type === 'pdf') {
    const companyClean = company.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    const timestamp = Date.now();
    return `${companyClean}-${clean}-${timestamp}.pdf`;
  } else if (company && type === 'logo') {
    const companyClean = company.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    const ext = path.extname(clean) || '.png';
    return `${companyClean}-logo${ext}`;
  }
  
  return clean;
}

/**
 * Compress image if needed
 */
async function compressImage(buffer, filename) {
  try {
    const ext = path.extname(filename).toLowerCase();
    
    if (['.jpg', '.jpeg', '.png', '.webp'].includes(ext)) {
      console.log(`🗜️  Compressing image: ${filename}`);
      
      const compressed = await sharp(buffer)
        .resize(800, 600, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .jpeg({ 
          quality: 85,
          progressive: true 
        })
        .toBuffer();
      
      const originalSize = buffer.length;
      const compressedSize = compressed.length;
      const savings = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
      
      console.log(`📊 Compression: ${originalSize} → ${compressedSize} bytes (${savings}% saved)`);
      
      return compressed;
    }
    
    return buffer;
  } catch (error) {
    console.log(`⚠️  Could not compress ${filename}, using original`);
    return buffer;
  }
}

/**
 * Upload file to specific bucket with proper naming
 */
async function uploadToSupabase(filename, fileBuffer, contentType, bucket, company = '', type = '') {
  try {
    const cleanName = cleanFilename(filename, company, type);
    
    // Compress if it's an image
    let processedBuffer = fileBuffer;
    if (type === 'logo') {
      processedBuffer = await compressImage(fileBuffer, filename);
    }
    
    const uploadURL = `${SUPABASE_URL}/storage/v1/object/${bucket}/${cleanName}`;
    const response = await axios.put(
      uploadURL,
      processedBuffer,
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': contentType,
        },
      }
    );
    
    if (response.status === 200) {
      const publicURL = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${cleanName}`;
      console.log(`✅ Uploaded: ${cleanName} (${type})`);
      return publicURL;
    }
    return null;
  } catch (error) {
    console.error(`❌ Upload failed for ${filename}:`, error.response?.data || error.message);
    return null;
  }
}

/**
 * Get or create company record
 */
async function getOrCreateCompany(companyName, logoAttachments) {
  if (!companyName) return null;
  
  try {
    // Check if company already exists
    const { data: existingCompany, error: selectError } = await supabase
      .from(COMPANIES_TABLE)
      .select('*')
      .eq('name', companyName)
      .single();
    
    if (existingCompany && !selectError) {
      console.log(`🏢 Company exists: ${companyName}`);
      return existingCompany.id;
    }
    
    // Create new company
    let logoUrl = null;
    if (logoAttachments && logoAttachments.length > 0) {
      const logo = logoAttachments[0];
      console.log(`📥 Downloading logo for ${companyName}: ${logo.filename}`);
      
      const downloadResponse = await axios.get(logo.url, { responseType: 'arraybuffer' });
      logoUrl = await uploadToSupabase(
        logo.filename, 
        downloadResponse.data, 
        logo.type, 
        LOGO_BUCKET, 
        companyName, 
        'logo'
      );
    }
    
    const { data: newCompany, error: insertError } = await supabase
      .from(COMPANIES_TABLE)
      .insert([{
        name: companyName,
        logo_url: logoUrl,
        slug: companyName.toLowerCase().replace(/[^a-z0-9]/g, '-')
      }])
      .select()
      .single();
    
    if (insertError) {
      console.error(`❌ Failed to create company ${companyName}:`, insertError);
      return null;
    }
    
    console.log(`🏢 Created company: ${companyName}`);
    return newCompany.id;
    
  } catch (error) {
    console.error(`❌ Error with company ${companyName}:`, error.message);
    return null;
  }
}

/**
 * Process PDF attachment
 */
async function processPDF(attachments, companyName, caseName) {
  if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
    return null;
  }
  
  const pdf = attachments[0];
  const { url, filename, type: contentType } = pdf;
  
  try {
    console.log(`📥 Downloading PDF: ${filename}`);
    const downloadResponse = await axios.get(url, { responseType: 'arraybuffer' });
    
    const pdfUrl = await uploadToSupabase(
      filename, 
      downloadResponse.data, 
      contentType, 
      PDF_BUCKET, 
      companyName, 
      'pdf'
    );
    
    return pdfUrl;
  } catch (error) {
    console.error(`❌ Error processing PDF ${filename}:`, error.message);
    return url; // Fallback to original URL
  }
}

/**
 * Insert case study record
 */
async function insertCaseStudy(record, companyId) {
  const fields = record.fields;
  
  // Process PDF
  const pdfUrl = await processPDF(fields.PDF, fields.Company, fields.Name);
  
  const data = {
    airtable_id: record.id,
    name: fields.Name || null,
    company_id: companyId,
    company: fields.Company || null,
    organizer: fields.Organizer || null,
    type_field: fields.Type ? JSON.stringify(fields.Type) : null,
    objective: fields.Objective ? JSON.stringify(fields.Objective) : null,
    category: fields.Category ? JSON.stringify(fields.Category) : null,
    image_tags_extra: fields['Image Tags Extra'] ? JSON.stringify(fields['Image Tags Extra']) : null,
    publish: fields.Publish || null,
    sort_field: fields.Sort || null,
    likes: fields.Likes || null,
    new_image_tag: fields['New Image Tag'] ? JSON.stringify(fields['New Image Tag']) : null,
    likes_filter: fields['Likes Filter'] ? JSON.stringify(fields['Likes Filter']) : null,
    likes_filter_formula: fields['Likes Filter Formula'] || null,
    creators_tag: fields['Creators Tag'] || null,
    market: fields.Market || null,
    pdf_url: pdfUrl,
    pdf: fields.PDF ? JSON.stringify(fields.PDF) : null
  };
  
  try {
    const { data: insertedData, error } = await supabase
      .from(MAIN_TABLE)
      .insert([data]);
    
    if (error) {
      console.error('❌ Error inserting case study:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error inserting case study:', error.message);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateOptimized() {
  console.log('🚀 Starting optimized Airtable to Supabase migration...\n');
  
  let totalRecords = 0;
  let successfulRecords = 0;
  let processedCompanies = new Set();
  
  try {
    await new Promise((resolve, reject) => {
      base(AIRTABLE_TABLE_NAME).select({
        view: 'Grid view'
      }).eachPage(async (records, fetchNextPage) => {
        console.log(`📄 Processing ${records.length} records...`);
        
        for (const record of records) {
          totalRecords++;
          const fields = record.fields;
          
          try {
            // Get or create company
            let companyId = null;
            if (fields.Company && !processedCompanies.has(fields.Company)) {
              companyId = await getOrCreateCompany(fields.Company, fields.Logo);
              processedCompanies.add(fields.Company);
            } else if (fields.Company) {
              // Get existing company ID
              const { data: existingCompany } = await supabase
                .from(COMPANIES_TABLE)
                .select('id')
                .eq('name', fields.Company)
                .single();
              companyId = existingCompany?.id;
            }
            
            // Insert case study
            const success = await insertCaseStudy(record, companyId);
            if (success) {
              successfulRecords++;
            }
            
            if (totalRecords % 10 === 0) {
              console.log(`📈 Progress: ${successfulRecords}/${totalRecords} records migrated`);
            }
            
          } catch (error) {
            console.error(`❌ Error processing record ${record.id}:`, error.message);
          }
        }
        
        fetchNextPage();
      }, (err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
    
    console.log(`\n🎉 Migration complete!`);
    console.log(`📊 Results:`);
    console.log(`   • ${successfulRecords}/${totalRecords} case studies migrated`);
    console.log(`   • ${processedCompanies.size} companies processed`);
    console.log(`   • Files organized in separate buckets:`);
    console.log(`     - Logos: ${LOGO_BUCKET}`);
    console.log(`     - PDFs: ${PDF_BUCKET}`);
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
  }
}

// Run the optimized migration
migrateOptimized().catch(err => {
  console.error("💥 Critical error:", err);
});
