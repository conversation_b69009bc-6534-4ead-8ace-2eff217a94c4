const { createClient } = require('@supabase/supabase-js');
const GoogleDriveUploader = require('./google-drive-uploader');
require('dotenv').config();

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

class AirtableToGoogleDriveMigration {
  constructor() {
    this.driveUploader = new GoogleDriveUploader();
    this.stats = {
      totalRecords: 0,
      processed: 0,
      uploaded: 0,
      skipped: 0,
      errors: 0,
      totalSize: 0
    };
  }

  /**
   * Initialize the migration
   */
  async initialize() {
    console.log('🚀 Airtable to Google Drive Migration');
    console.log('=====================================');
    console.log('📋 This will upload all PDFs from original Airtable URLs to Google Drive');
    console.log('💡 Since case-study-pdfs bucket is empty, we\'ll use original Airtable URLs');
    console.log('');

    // Initialize Google Drive
    const driveReady = await this.driveUploader.initialize();
    if (!driveReady) {
      throw new Error('Failed to initialize Google Drive API');
    }

    // Check Google Drive folder
    const folderInfo = await this.driveUploader.getFolderInfo();
    console.log(`📁 Google Drive folder: ${folderInfo.fileCount} files, ${this.driveUploader.formatBytes(folderInfo.totalSize)}`);
    console.log('');

    return true;
  }

  /**
   * Get all records that need migration
   */
  async getRecordsToMigrate() {
    try {
      const { data: records, error } = await supabase
        .from('airtable_data')
        .select('id, name, company, pdf_url, pdf, created_at')
        .not('pdf', 'is', null)
        .order('created_at', { ascending: true });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      console.log(`📊 Found ${records.length} total records with PDF data`);
      
      // Filter records that need migration (those with broken Supabase URLs or no pdf_url)
      const recordsToMigrate = records.filter(record => {
        // Parse PDF data to check if we have original Airtable URL
        try {
          const pdfData = JSON.parse(record.pdf);
          return pdfData && pdfData.length > 0 && pdfData[0].url;
        } catch {
          return false;
        }
      });

      console.log(`🔄 Records that can be migrated: ${recordsToMigrate.length}`);
      console.log('');

      return recordsToMigrate;
    } catch (error) {
      console.error('❌ Error fetching records:', error.message);
      throw error;
    }
  }

  /**
   * Migrate a single record
   */
  async migrateRecord(record) {
    try {
      this.stats.processed++;
      
      console.log(`📄 Processing: ${record.name} (${record.company})`);
      
      // Parse PDF data to get original Airtable URL
      let pdfData;
      try {
        pdfData = JSON.parse(record.pdf);
      } catch (parseError) {
        throw new Error(`Could not parse PDF data: ${parseError.message}`);
      }

      if (!pdfData || pdfData.length === 0 || !pdfData[0].url) {
        throw new Error('No valid PDF URL found in data');
      }

      const airtableUrl = pdfData[0].url;
      const originalFilename = pdfData[0].filename || 'document.pdf';
      
      console.log(`   📥 Original URL: ${airtableUrl.substring(0, 60)}...`);
      
      // Generate clean Google Drive filename
      const timestamp = new Date(record.created_at).getTime();
      const driveFilename = this.driveUploader.generateFileName(
        record.company, 
        originalFilename, 
        timestamp
      );
      
      console.log(`   📝 Google Drive filename: ${driveFilename}`);
      
      // Check if file already exists in Google Drive
      const existingFile = await this.driveUploader.fileExists(driveFilename);
      if (existingFile.exists) {
        console.log(`   ⏭️  File already exists in Google Drive, updating database...`);
        
        // Update database with Google Drive URL
        const { error: updateError } = await supabase
          .from('airtable_data')
          .update({ pdf_url: existingFile.viewLink })
          .eq('id', record.id);
        
        if (updateError) {
          throw new Error(`Database update failed: ${updateError.message}`);
        }
        
        this.stats.skipped++;
        console.log(`   ✅ Database updated with existing Google Drive link`);
        return;
      }
      
      // Upload to Google Drive from Airtable URL
      const uploadResult = await this.driveUploader.uploadFromUrl(airtableUrl, driveFilename);
      
      // Update database with Google Drive URL
      const { error: updateError } = await supabase
        .from('airtable_data')
        .update({ pdf_url: uploadResult.viewLink })
        .eq('id', record.id);
      
      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`);
      }
      
      this.stats.uploaded++;
      this.stats.totalSize += uploadResult.size;
      
      console.log(`   ✅ Migration complete: ${this.driveUploader.formatBytes(uploadResult.size)}`);
      console.log(`   🔗 Google Drive link: ${uploadResult.viewLink}`);
      
    } catch (error) {
      this.stats.errors++;
      console.error(`   ❌ Migration failed: ${error.message}`);
      
      // Continue with next record instead of stopping
      return;
    }
  }

  /**
   * Run the complete migration
   */
  async migrate() {
    try {
      await this.initialize();
      
      const records = await this.getRecordsToMigrate();
      this.stats.totalRecords = records.length;
      
      if (records.length === 0) {
        console.log('🎉 No records need migration!');
        return;
      }
      
      console.log(`🚀 Starting migration of ${records.length} records...`);
      console.log('');
      
      for (const record of records) {
        await this.migrateRecord(record);
        
        // Progress update every 10 records
        if (this.stats.processed % 10 === 0) {
          console.log('');
          console.log(`📈 Progress: ${this.stats.processed}/${this.stats.totalRecords} records processed`);
          console.log(`   ✅ Uploaded: ${this.stats.uploaded}`);
          console.log(`   ⏭️  Skipped: ${this.stats.skipped}`);
          console.log(`   ❌ Errors: ${this.stats.errors}`);
          console.log(`   💾 Total uploaded: ${this.driveUploader.formatBytes(this.stats.totalSize)}`);
          console.log('');
        }
        
        // Small delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // Final results
      console.log('');
      console.log('🎉 MIGRATION COMPLETE!');
      console.log('======================');
      console.log(`📊 Total records: ${this.stats.totalRecords}`);
      console.log(`✅ Successfully uploaded: ${this.stats.uploaded}`);
      console.log(`⏭️  Already existed: ${this.stats.skipped}`);
      console.log(`❌ Errors: ${this.stats.errors}`);
      console.log(`💾 Total data uploaded: ${this.driveUploader.formatBytes(this.stats.totalSize)}`);
      console.log('');
      console.log('📋 Results:');
      console.log('✅ All case study PDFs are now stored in Google Drive');
      console.log('✅ Database records updated with Google Drive links');
      console.log('✅ Supabase storage usage minimized (only logos remain)');
      console.log('✅ All 800 records maintain full data integrity');
      
    } catch (error) {
      console.error('💥 Migration failed:', error.message);
      process.exit(1);
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const migration = new AirtableToGoogleDriveMigration();
  migration.migrate().catch(err => {
    console.error('💥 Critical error:', err);
    process.exit(1);
  });
}

module.exports = AirtableToGoogleDriveMigration;
