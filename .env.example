# Google Drive Configuration
# Get folder ID from Google Drive URL: https://drive.google.com/drive/folders/YOUR_FOLDER_ID_HERE
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id_here
GOOGLE_DRIVE_CREDENTIALS_PATH=./google-drive-credentials.json

# Supabase Configuration (already configured)
SUPABASE_URL=https://rnpxnaqfoqdivxrlozfr.supabase.co
SUPABASE_KEY=your_supabase_key_here

# Instructions:
# 1. Copy this file to .env
# 2. Replace your_google_drive_folder_id_here with actual folder ID
# 3. Place your google-drive-credentials.json file in project root
# 4. Run: node test-google-drive.js to verify setup
