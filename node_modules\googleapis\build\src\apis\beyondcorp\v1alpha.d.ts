import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace beyondcorp_v1alpha {
    export interface Options extends GlobalOptions {
        version: 'v1alpha';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * BeyondCorp API
     *
     * Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity solutions.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const beyondcorp = google.beyondcorp('v1alpha');
     * ```
     */
    export class Beyondcorp {
        context: APIRequestContext;
        organizations: Resource$Organizations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Allocated connection of the AppGateway.
     */
    export interface Schema$AllocatedConnection {
        /**
         * Required. The ingress port of an allocated connection
         */
        ingressPort?: number | null;
        /**
         * Required. The PSC uri of an allocated connection
         */
        pscUri?: string | null;
    }
    /**
     * A BeyondCorp AppGateway resource represents a BeyondCorp protected AppGateway to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected AppGateway. Multiple connectors can be authorised for a single AppGateway.
     */
    export interface Schema$AppGateway {
        /**
         * Output only. A list of connections allocated for the Gateway
         */
        allocatedConnections?: Schema$AllocatedConnection[];
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the AppGateway. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Required. The type of hosting used by the AppGateway.
         */
        hostType?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Unique resource name of the AppGateway. The name is ignored when creating an AppGateway.
         */
        name?: string | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. The current state of the AppGateway.
         */
        state?: string | null;
        /**
         * Required. The type of network connectivity used by the AppGateway.
         */
        type?: string | null;
        /**
         * Output only. A unique identifier for the instance generated by the system.
         */
        uid?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
        /**
         * Output only. Server-defined URI for this resource.
         */
        uri?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$AppGatewayOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * ApplicationEndpoint represents a remote application endpoint.
     */
    export interface Schema$ApplicationEndpoint {
        /**
         * Required. Hostname or IP address of the remote application endpoint.
         */
        host?: string | null;
        /**
         * Required. Port of the remote application endpoint.
         */
        port?: number | null;
    }
    /**
     * The configuration for Pub/Sub messaging for the connector.
     */
    export interface Schema$CloudPubSubNotificationConfig {
        /**
         * The Pub/Sub subscription the connector uses to receive notifications.
         */
        pubsubSubscription?: string | null;
    }
    /**
     * ConnectionConfig represents a Connection Configuration object.
     */
    export interface Schema$CloudSecurityZerotrustApplinkAppConnectorProtoConnectionConfig {
        /**
         * application_endpoint is the endpoint of the application the form of host:port. For example, "localhost:80".
         */
        applicationEndpoint?: string | null;
        /**
         * application_name represents the given name of the application the connection is connecting with.
         */
        applicationName?: string | null;
        /**
         * gateway lists all instances running a gateway in GCP. They all connect to a connector on the host.
         */
        gateway?: Schema$CloudSecurityZerotrustApplinkAppConnectorProtoGateway[];
        /**
         * name is the unique ID for each connection. TODO(b/190732451) returns connection name from user-specified name in config. Now, name = ${application_name\}:${application_endpoint\}
         */
        name?: string | null;
        /**
         * project represents the consumer project the connection belongs to.
         */
        project?: string | null;
        /**
         * tunnels_per_gateway reflects the number of tunnels between a connector and a gateway.
         */
        tunnelsPerGateway?: number | null;
        /**
         * user_port specifies the reserved port on gateways for user connections.
         */
        userPort?: number | null;
    }
    /**
     * ConnectorDetails reflects the details of a connector.
     */
    export interface Schema$CloudSecurityZerotrustApplinkAppConnectorProtoConnectorDetails {
    }
    /**
     * Gateway represents a GCE VM Instance endpoint for use by IAP TCP.
     */
    export interface Schema$CloudSecurityZerotrustApplinkAppConnectorProtoGateway {
        /**
         * interface specifies the network interface of the gateway to connect to.
         */
        interface?: string | null;
        /**
         * name is the name of an instance running a gateway. It is the unique ID for a gateway. All gateways under the same connection have the same prefix. It is derived from the gateway URL. For example, name=${instance\} assuming a gateway URL. https://www.googleapis.com/compute/${version\}/projects/${project\}/zones/${zone\}/instances/${instance\}
         */
        name?: string | null;
        /**
         * port specifies the port of the gateway for tunnel connections from the connectors.
         */
        port?: number | null;
        /**
         * project is the tenant project the gateway belongs to. Different from the project in the connection, it is a BeyondCorpAPI internally created project to manage all the gateways. It is sharing the same network with the consumer project user owned. It is derived from the gateway URL. For example, project=${project\} assuming a gateway URL. https://www.googleapis.com/compute/${version\}/projects/${project\}/zones/${zone\}/instances/${instance\}
         */
        project?: string | null;
        /**
         * self_link is the gateway URL in the form https://www.googleapis.com/compute/${version\}/projects/${project\}/zones/${zone\}/instances/${instance\}
         */
        selfLink?: string | null;
        /**
         * zone represents the zone the instance belongs. It is derived from the gateway URL. For example, zone=${zone\} assuming a gateway URL. https://www.googleapis.com/compute/${version\}/projects/${project\}/zones/${zone\}/instances/${instance\}
         */
        zone?: string | null;
    }
    /**
     * LogAgentDetails reflects the details of a log agent.
     */
    export interface Schema$CloudSecurityZerotrustApplinkLogagentProtoLogAgentDetails {
    }
    /**
     * A BeyondCorp Connection resource represents a BeyondCorp protected connection to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected connection. Multiple connectors can be authorised for a single Connection.
     */
    export interface Schema$Connection {
        /**
         * Required. Address of the remote application endpoint for the BeyondCorp Connection.
         */
        applicationEndpoint?: Schema$ApplicationEndpoint;
        /**
         * Optional. List of [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to be associated with this Connection.
         */
        connectors?: string[] | null;
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the connection. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Optional. Gateway used by the connection.
         */
        gateway?: Schema$Gateway;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Unique resource name of the connection. The name is ignored when creating a connection.
         */
        name?: string | null;
        /**
         * Output only. The current state of the connection.
         */
        state?: string | null;
        /**
         * Required. The type of network connectivity used by the connection.
         */
        type?: string | null;
        /**
         * Output only. A unique identifier for the instance generated by the system.
         */
        uid?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * Details of the Connection.
     */
    export interface Schema$ConnectionDetails {
        /**
         * A BeyondCorp Connection in the project.
         */
        connection?: Schema$Connection;
        /**
         * If type=GCP_REGIONAL_MIG, contains most recent VM instances, like "https://www.googleapis.com/compute/v1/projects/{project_id\}/zones/{zone_id\}/instances/{instance_id\}".
         */
        recentMigVms?: string[] | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$ConnectionOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * A BeyondCorp connector resource that represents an application facing component deployed proximal to and with direct access to the application instances. It is used to establish connectivity between the remote enterprise environment and GCP. It initiates connections to the applications and can proxy the data from users over the connection.
     */
    export interface Schema$Connector {
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the connector. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Unique resource name of the connector. The name is ignored when creating a connector.
         */
        name?: string | null;
        /**
         * Required. Principal information about the Identity of the connector.
         */
        principalInfo?: Schema$PrincipalInfo;
        /**
         * Optional. Resource info of the connector.
         */
        resourceInfo?: Schema$ResourceInfo;
        /**
         * Output only. The current state of the connector.
         */
        state?: string | null;
        /**
         * Output only. A unique identifier for the instance generated by the system.
         */
        uid?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * ConnectorInstanceConfig defines the instance config of a connector.
     */
    export interface Schema$ConnectorInstanceConfig {
        /**
         * ImageConfig defines the GCR images to run for the remote agent's control plane.
         */
        imageConfig?: Schema$ImageConfig;
        /**
         * The SLM instance agent configuration.
         */
        instanceConfig?: {
            [key: string]: any;
        } | null;
        /**
         * NotificationConfig defines the notification mechanism that the remote instance should subscribe to in order to receive notification.
         */
        notificationConfig?: Schema$NotificationConfig;
        /**
         * Required. A monotonically increasing number generated and maintained by the API provider. Every time a config changes in the backend, the sequenceNumber should be bumped up to reflect the change.
         */
        sequenceNumber?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$ConnectorOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * ContainerHealthDetails reflects the health details of a container.
     */
    export interface Schema$ContainerHealthDetails {
        /**
         * The version of the current config.
         */
        currentConfigVersion?: string | null;
        /**
         * The latest error message.
         */
        errorMsg?: string | null;
        /**
         * The version of the expected config.
         */
        expectedConfigVersion?: string | null;
        /**
         * The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.
         */
        extendedStatus?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Gateway represents a user facing component that serves as an entrance to enable connectivity.
     */
    export interface Schema$Gateway {
        /**
         * Required. The type of hosting used by the gateway.
         */
        type?: string | null;
        /**
         * Output only. Server-defined URI for this resource.
         */
        uri?: string | null;
        /**
         * Output only. User port reserved on the gateways for this connection, if not specified or zero, the default port is 19443.
         */
        userPort?: number | null;
    }
    /**
     * A BeyondCorp AppConnection resource represents a BeyondCorp protected AppConnection to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected AppConnection. Multiple connectors can be authorised for a single AppConnection.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection {
        /**
         * Required. Address of the remote application endpoint for the BeyondCorp AppConnection.
         */
        applicationEndpoint?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint;
        /**
         * Optional. List of [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to be associated with this AppConnection.
         */
        connectors?: string[] | null;
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the AppConnection. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Optional. Gateway used by the AppConnection.
         */
        gateway?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Unique resource name of the AppConnection. The name is ignored when creating a AppConnection.
         */
        name?: string | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. The current state of the AppConnection.
         */
        state?: string | null;
        /**
         * Required. The type of network connectivity used by the AppConnection.
         */
        type?: string | null;
        /**
         * Output only. A unique identifier for the instance generated by the system.
         */
        uid?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * ApplicationEndpoint represents a remote application endpoint.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint {
        /**
         * Required. Hostname or IP address of the remote application endpoint.
         */
        host?: string | null;
        /**
         * Required. Port of the remote application endpoint.
         */
        port?: number | null;
    }
    /**
     * Gateway represents a user facing component that serves as an entrance to enable connectivity.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway {
        /**
         * Required. AppGateway name in following format: `projects/{project_id\}/locations/{location_id\}/appgateways/{gateway_id\}`
         */
        appGateway?: string | null;
        /**
         * Output only. Ingress port reserved on the gateways for this AppConnection, if not specified or zero, the default port is 19443.
         */
        ingressPort?: number | null;
        /**
         * Output only. L7 private service connection for this resource.
         */
        l7psc?: string | null;
        /**
         * Required. The type of hosting used by the gateway.
         */
        type?: string | null;
        /**
         * Output only. Server-defined URI for this resource.
         */
        uri?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Response message for BeyondCorp.ListAppConnections.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse {
        /**
         * A list of BeyondCorp AppConnections in the project.
         */
        appConnections?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for BeyondCorp.ResolveAppConnections.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse {
        /**
         * A list of BeyondCorp AppConnections with details in the project.
         */
        appConnectionDetails?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Details of the AppConnection.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails {
        /**
         * A BeyondCorp AppConnection in the project.
         */
        appConnection?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection;
        /**
         * If type=GCP_REGIONAL_MIG, contains most recent VM instances, like `https://www.googleapis.com/compute/v1/projects/{project_id\}/zones/{zone_id\}/instances/{instance_id\}`.
         */
        recentMigVms?: string[] | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectionsV1AppConnectionOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * A BeyondCorp connector resource that represents an application facing component deployed proximal to and with direct access to the application instances. It is used to establish connectivity between the remote enterprise environment and GCP. It initiates connections to the applications and can proxy the data from users over the connection.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector {
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the AppConnector. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Unique resource name of the AppConnector. The name is ignored when creating a AppConnector.
         */
        name?: string | null;
        /**
         * Required. Principal information about the Identity of the AppConnector.
         */
        principalInfo?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo;
        /**
         * Optional. Resource info of the connector.
         */
        resourceInfo?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo;
        /**
         * Output only. The current state of the AppConnector.
         */
        state?: string | null;
        /**
         * Output only. A unique identifier for the instance generated by the system.
         */
        uid?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * AppConnectorInstanceConfig defines the instance config of a AppConnector.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig {
        /**
         * ImageConfig defines the GCR images to run for the remote agent's control plane.
         */
        imageConfig?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig;
        /**
         * The SLM instance agent configuration.
         */
        instanceConfig?: {
            [key: string]: any;
        } | null;
        /**
         * NotificationConfig defines the notification mechanism that the remote instance should subscribe to in order to receive notification.
         */
        notificationConfig?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig;
        /**
         * Required. A monotonically increasing number generated and maintained by the API provider. Every time a config changes in the backend, the sequenceNumber should be bumped up to reflect the change.
         */
        sequenceNumber?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * PrincipalInfo represents an Identity oneof.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo {
        /**
         * A GCP service account.
         */
        serviceAccount?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount;
    }
    /**
     * ServiceAccount represents a GCP service account.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount {
        /**
         * Email address of the service account.
         */
        email?: string | null;
    }
    /**
     * ContainerHealthDetails reflects the health details of a container.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaContainerHealthDetails {
        /**
         * The version of the current config.
         */
        currentConfigVersion?: string | null;
        /**
         * The latest error message.
         */
        errorMsg?: string | null;
        /**
         * The version of the expected config.
         */
        expectedConfigVersion?: string | null;
        /**
         * The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.
         */
        extendedStatus?: {
            [key: string]: string;
        } | null;
    }
    /**
     * ImageConfig defines the control plane images to run.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig {
        /**
         * The stable image that the remote agent will fallback to if the target image fails. Format would be a gcr image path, e.g.: gcr.io/PROJECT-ID/my-image:tag1
         */
        stableImage?: string | null;
        /**
         * The initial image the remote agent will attempt to run for the control plane. Format would be a gcr image path, e.g.: gcr.io/PROJECT-ID/my-image:tag1
         */
        targetImage?: string | null;
    }
    /**
     * Response message for BeyondCorp.ListAppConnectors.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse {
        /**
         * A list of BeyondCorp AppConnectors in the project.
         */
        appConnectors?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * NotificationConfig defines the mechanisms to notify instance agent.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig {
        /**
         * Cloud Pub/Sub Configuration to receive notifications.
         */
        pubsubNotification?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig;
    }
    /**
     * The configuration for Pub/Sub messaging for the AppConnector.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig {
        /**
         * The Pub/Sub subscription the AppConnector uses to receive notifications.
         */
        pubsubSubscription?: string | null;
    }
    /**
     * RemoteAgentDetails reflects the details of a remote agent.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaRemoteAgentDetails {
    }
    /**
     * Request report the connector status.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest {
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string | null;
        /**
         * Required. Resource info of the connector.
         */
        resourceInfo?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean | null;
    }
    /**
     * Response message for BeyondCorp.ResolveInstanceConfig.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse {
        /**
         * AppConnectorInstanceConfig.
         */
        instanceConfig?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig;
    }
    /**
     * ResourceInfo represents the information/status of an app connector resource. Such as: - remote_agent - container - runtime - appgateway - appconnector - appconnection - tunnel - logagent
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo {
        /**
         * Required. Unique Id for the resource.
         */
        id?: string | null;
        /**
         * Specific details for the resource. This is for internal use only.
         */
        resource?: {
            [key: string]: any;
        } | null;
        /**
         * Overall health status. Overall status is derived based on the status of each sub level resources.
         */
        status?: string | null;
        /**
         * List of Info for the sub level resources.
         */
        sub?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo[];
        /**
         * The timestamp to collect the info. It is suggested to be set by the topmost level resource only.
         */
        time?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1AppConnectorOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * ContainerHealthDetails reflects the health details of a container.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1ContainerHealthDetails {
        /**
         * The version of the current config.
         */
        currentConfigVersion?: string | null;
        /**
         * The latest error message.
         */
        errorMsg?: string | null;
        /**
         * The version of the expected config.
         */
        expectedConfigVersion?: string | null;
        /**
         * The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.
         */
        extendedStatus?: {
            [key: string]: string;
        } | null;
    }
    /**
     * RemoteAgentDetails reflects the details of a remote agent.
     */
    export interface Schema$GoogleCloudBeyondcorpAppconnectorsV1RemoteAgentDetails {
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpAppgatewaysV1AppGatewayOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Message to capture group information
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaGroup {
        /**
         * The group email id
         */
        email?: string | null;
        /**
         * Google group id
         */
        id?: string | null;
    }
    /**
     * Message for response to listing PartnerTenants.
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse {
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * The list of PartnerTenant objects.
         */
        partnerTenants?: Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant[];
    }
    /**
     * Metadata associated with PartnerTenant and is provided by the Partner.
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata {
        /**
         * Optional. UUID used by the Partner to refer to the PartnerTenant in their internal systems.
         */
        internalTenantId?: string | null;
        /**
         * Optional. UUID used by the Partner to refer to the PartnerTenant in their internal systems.
         */
        partnerTenantId?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerServiceOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the caller has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Information about a BeyoncCorp Enterprise PartnerTenant.
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant {
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary caller-provided name for the PartnerTenant. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Optional. Group information for the users enabled to use the partnerTenant. If the group information is not provided then the partnerTenant will be enabled for all users.
         */
        group?: Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaGroup;
        /**
         * Output only. Unique resource name of the PartnerTenant. The name is ignored when creating PartnerTenant.
         */
        name?: string | null;
        /**
         * Optional. Metadata provided by the Partner associated with PartnerTenant.
         */
        partnerMetadata?: Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpPartnerservicesV1mainPartnerServiceOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the caller has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * The configuration that was applied to generate the result.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig {
        /**
         * Output only. Aggregation type applied.
         */
        aggregation?: string | null;
        /**
         * Output only. Customised grouping applied.
         */
        customGrouping?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping;
        /**
         * Output only. Ending time for the duration for which insight was pulled.
         */
        endTime?: string | null;
        /**
         * Output only. Filters applied.
         */
        fieldFilter?: string | null;
        /**
         * Output only. Group id of the grouping applied.
         */
        group?: string | null;
        /**
         * Output only. Starting time for the duration for which insight was pulled.
         */
        startTime?: string | null;
    }
    /**
     * The response for the configured insight.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse {
        /**
         * Output only. Applied insight config to generate the result data rows.
         */
        appliedConfig?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig;
        /**
         * Output only. Next page token to be fetched. Set to empty or NULL if there are no more pages available.
         */
        nextPageToken?: string | null;
        /**
         * Output only. Result rows returned containing the required value(s) for configured insight.
         */
        rows?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow[];
    }
    /**
     * Customised grouping option that allows setting the group_by fields and also the filters togather for a configured insight request.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping {
        /**
         * Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        fieldFilter?: string | null;
        /**
         * Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.
         */
        groupFields?: string[] | null;
    }
    /**
     * The Insight object with configuration that was returned and actual list of records.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight {
        /**
         * Output only. Applied insight config to generate the result data rows.
         */
        appliedConfig?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig;
        /**
         * Output only. Metadata for the Insight.
         */
        metadata?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata;
        /**
         * Output only. The insight resource name. e.g. `organizations/{organization_id\}/locations/{location_id\}/insights/{insight_id\}` OR `projects/{project_id\}/locations/{location_id\}/insights/{insight_id\}`.
         */
        name?: string | null;
        /**
         * Output only. Result rows returned containing the required value(s).
         */
        rows?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow[];
    }
    /**
     * Insight filters, groupings and aggregations that can be applied for the insight. Examples: aggregations, groups, field filters.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata {
        /**
         * Output only. List of aggregation types available for insight.
         */
        aggregations?: string[] | null;
        /**
         * Output only. Category of the insight.
         */
        category?: string | null;
        /**
         * Output only. Common name of the insight.
         */
        displayName?: string | null;
        /**
         * Output only. List of fields available for insight.
         */
        fields?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField[];
        /**
         * Output only. List of groupings available for insight.
         */
        groups?: string[] | null;
        /**
         * Output only. Sub-Category of the insight.
         */
        subCategory?: string | null;
        /**
         * Output only. Type of the insight. It is metadata describing whether the insight is a metric (e.g. count) or a report (e.g. list, status).
         */
        type?: string | null;
    }
    /**
     * Field metadata. Commonly understandable name and description for the field. Multiple such fields constitute the Insight.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField {
        /**
         * Output only. Description of the field.
         */
        description?: string | null;
        /**
         * Output only. Name of the field.
         */
        displayName?: string | null;
        /**
         * Output only. Indicates whether the field can be used for filtering.
         */
        filterable?: boolean | null;
        /**
         * Output only. Field name to be used in filter while requesting configured insight filtered on this field.
         */
        filterAlias?: string | null;
        /**
         * Output only. Indicates whether the field can be used for grouping in custom grouping request.
         */
        groupable?: boolean | null;
        /**
         * Output only. Field id for which this is the metadata.
         */
        id?: string | null;
    }
    /**
     * The response for the list of insights.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse {
        /**
         * Output only. List of all insights.
         */
        insights?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight[];
        /**
         * Output only. Next page token to be fetched. Set to empty or NULL if there are no more pages available.
         */
        nextPageToken?: string | null;
    }
    /**
     * Row of the fetch response consisting of a set of entries.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow {
        /**
         * Output only. Columns/entries/key-vals in the result.
         */
        fieldValues?: Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal[];
    }
    /**
     * Column or key value pair from the request as part of key to use in query or a single pair of the fetch response.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal {
        /**
         * Output only. Name of the field.
         */
        displayName?: string | null;
        /**
         * Output only. Field name to be used in filter while requesting configured insight filtered on this field.
         */
        filterAlias?: string | null;
        /**
         * Output only. Field id.
         */
        id?: string | null;
        /**
         * Output only. Value of the field in string format. Acceptable values are strings or numbers.
         */
        value?: string | null;
    }
    /**
     * Response message for BeyondCorp.CancelSubscription
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse {
        /**
         * Time when the cancellation will become effective
         */
        effectiveCancellationTime?: string | null;
    }
    /**
     * Response message for BeyondCorp.ListSubscriptions.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse {
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of BeyondCorp Subscriptions in the organization.
         */
        subscriptions?: Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription[];
    }
    /**
     * Response message for BeyondCorp.RestartSubscription
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse {
    }
    /**
     * A BeyondCorp Subscription resource represents BeyondCorp Enterprise Subscription. BeyondCorp Enterprise Subscription enables BeyondCorp Enterprise permium features for an organization.
     */
    export interface Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription {
        /**
         * Output only. Represents that, if subscription will renew or end when the term ends.
         */
        autoRenewEnabled?: boolean | null;
        /**
         * Optional. Name of the billing account in the format. e.g. billingAccounts/123456-123456-123456 Required if Subscription is of Paid type.
         */
        billingAccount?: string | null;
        /**
         * Output only. Create time of the subscription.
         */
        createTime?: string | null;
        /**
         * Optional. Whether the subscription is being created as part of the Citrix flow. If this field is set to true, the subscription should have both the start_time and end_time set in the request and the billing account used will be the Citrix master billing account regardless of what its set to in the request. This field can only be set to true in create requests.
         */
        csgCustomer?: boolean | null;
        /**
         * Optional. End time of the subscription.
         */
        endTime?: string | null;
        /**
         * Identifier. Unique resource name of the Subscription. The name is ignored when creating a subscription.
         */
        name?: string | null;
        /**
         * Optional. Number of seats in the subscription.
         */
        seatCount?: string | null;
        /**
         * Required. SKU of subscription.
         */
        sku?: string | null;
        /**
         * Optional. Start time of the subscription.
         */
        startTime?: string | null;
        /**
         * Output only. The current state of the subscription.
         */
        state?: string | null;
        /**
         * Output only. Type of subscriber.
         */
        subscriberType?: string | null;
        /**
         * Required. Type of subscription.
         */
        type?: string | null;
    }
    /**
     * A Beyondcorp Application resource information.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication {
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the Application resource. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Required. Endpoint matchers associated with an application. A combination of hostname and ports as endpoint matcher is used to match the application. Match conditions for OR logic. An array of match conditions to allow for multiple matching criteria. The rule is considered a match if one the conditions are met. The conditions can be one of the following combination (Hostname), (Hostname & Ports) EXAMPLES: Hostname - ("*.abc.com"), ("xyz.abc.com") Hostname and Ports - ("abc.com" and "22"), ("abc.com" and "22,33") etc
         */
        endpointMatchers?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher[];
        /**
         * Identifier. Name of the resource.
         */
        name?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * EndpointMatcher contains the information of the endpoint that will match the application.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher {
        /**
         * Required. Hostname of the application.
         */
        hostname?: string | null;
        /**
         * Optional. Ports of the application.
         */
        ports?: number[] | null;
    }
    /**
     * The Hub message contains information pertaining to the regional data path deployments.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub {
        /**
         * Optional. Internet Gateway configuration.
         */
        internetGateway?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway;
    }
    /**
     * Represents the Internet Gateway configuration.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway {
        /**
         * Output only. List of IP addresses assigned to the Cloud NAT.
         */
        assignedIps?: string[] | null;
    }
    /**
     * Message for response to listing Applications.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse {
        /**
         * A list of BeyondCorp Application in the project.
         */
        applications?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Message for response to listing SecurityGateways.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse {
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of BeyondCorp SecurityGateway in the project.
         */
        securityGateways?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway[];
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Information about a BeyoncCorp SecurityGateway resource.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway {
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. An arbitrary user-provided name for the SecurityGateway. Cannot exceed 64 characters.
         */
        displayName?: string | null;
        /**
         * Output only. IP addresses that will be used for establishing connection to the endpoints.
         */
        externalIps?: string[] | null;
        /**
         * Optional. Map of Hubs that represents regional data path deployment with GCP region as a key.
         */
        hubs?: {
            [key: string]: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub;
        } | null;
        /**
         * Identifier. Name of the resource.
         */
        name?: string | null;
        /**
         * Output only. The operational state of the SecurityGateway.
         */
        state?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGatewayOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudBeyondcorpSecuritygatewaysV1SecurityGatewayOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$GoogleCloudLocationListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$GoogleCloudLocationLocation[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$GoogleCloudLocationLocation {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$GoogleIamV1AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$GoogleIamV1AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$GoogleIamV1AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$GoogleIamV1Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$GoogleTypeExpr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$GoogleIamV1Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$GoogleIamV1AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$GoogleIamV1Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$GoogleIamV1SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$GoogleIamV1Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$GoogleLongrunningCancelOperationRequest {
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$GoogleLongrunningListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$GoogleLongrunningOperation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunningOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$GoogleRpcStatus;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$GoogleRpcStatus {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$GoogleTypeExpr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * ImageConfig defines the control plane images to run.
     */
    export interface Schema$ImageConfig {
        /**
         * The stable image that the remote agent will fallback to if the target image fails.
         */
        stableImage?: string | null;
        /**
         * The initial image the remote agent will attempt to run for the control plane.
         */
        targetImage?: string | null;
    }
    /**
     * Response message for BeyondCorp.ListAppGateways.
     */
    export interface Schema$ListAppGatewaysResponse {
        /**
         * A list of BeyondCorp AppGateways in the project.
         */
        appGateways?: Schema$AppGateway[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for BeyondCorp.ListConnections.
     */
    export interface Schema$ListConnectionsResponse {
        /**
         * A list of BeyondCorp Connections in the project.
         */
        connections?: Schema$Connection[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for BeyondCorp.ListConnectors.
     */
    export interface Schema$ListConnectorsResponse {
        /**
         * A list of BeyondCorp Connectors in the project.
         */
        connectors?: Schema$Connector[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * NotificationConfig defines the mechanisms to notify instance agent.
     */
    export interface Schema$NotificationConfig {
        /**
         * Pub/Sub topic for Connector to subscribe and receive notifications from `projects/{project\}/topics/{pubsub_topic\}`
         */
        pubsubNotification?: Schema$CloudPubSubNotificationConfig;
    }
    /**
     * PrincipalInfo represents an Identity oneof.
     */
    export interface Schema$PrincipalInfo {
        /**
         * A GCP service account.
         */
        serviceAccount?: Schema$ServiceAccount;
    }
    /**
     * RemoteAgentDetails reflects the details of a remote agent.
     */
    export interface Schema$RemoteAgentDetails {
    }
    /**
     * Request report the connector status.
     */
    export interface Schema$ReportStatusRequest {
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string | null;
        /**
         * Required. Resource info of the connector.
         */
        resourceInfo?: Schema$ResourceInfo;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean | null;
    }
    /**
     * Response message for BeyondCorp.ResolveConnections.
     */
    export interface Schema$ResolveConnectionsResponse {
        /**
         * A list of BeyondCorp Connections with details in the project.
         */
        connectionDetails?: Schema$ConnectionDetails[];
        /**
         * A token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for BeyondCorp.ResolveInstanceConfig.
     */
    export interface Schema$ResolveInstanceConfigResponse {
        /**
         * ConnectorInstanceConfig.
         */
        instanceConfig?: Schema$ConnectorInstanceConfig;
    }
    /**
     * ResourceInfo represents the information/status of the associated resource.
     */
    export interface Schema$ResourceInfo {
        /**
         * Required. Unique Id for the resource.
         */
        id?: string | null;
        /**
         * Specific details for the resource.
         */
        resource?: {
            [key: string]: any;
        } | null;
        /**
         * Overall health status. Overall status is derived based on the status of each sub level resources.
         */
        status?: string | null;
        /**
         * List of Info for the sub level resources.
         */
        sub?: Schema$ResourceInfo[];
        /**
         * The timestamp to collect the info. It is suggested to be set by the topmost level resource only.
         */
        time?: string | null;
    }
    /**
     * ServiceAccount represents a GCP service account.
     */
    export interface Schema$ServiceAccount {
        /**
         * Email address of the service account.
         */
        email?: string | null;
    }
    /**
     * TunnelerError is an error proto for errors returned by the connection manager.
     */
    export interface Schema$Tunnelv1ProtoTunnelerError {
        /**
         * Original raw error
         */
        err?: string | null;
        /**
         * retryable isn't used for now, but we may want to reuse it in the future.
         */
        retryable?: boolean | null;
    }
    /**
     * TunnelerInfo contains metadata about tunneler launched by connection manager.
     */
    export interface Schema$Tunnelv1ProtoTunnelerInfo {
        /**
         * backoff_retry_count stores the number of times the tunneler has been retried by tunManager for current backoff sequence. Gets reset to 0 if time difference between 2 consecutive retries exceeds backoffRetryResetTime.
         */
        backoffRetryCount?: number | null;
        /**
         * id is the unique id of a tunneler.
         */
        id?: string | null;
        /**
         * latest_err stores the Error for the latest tunneler failure. Gets reset everytime the tunneler is retried by tunManager.
         */
        latestErr?: Schema$Tunnelv1ProtoTunnelerError;
        /**
         * latest_retry_time stores the time when the tunneler was last restarted.
         */
        latestRetryTime?: string | null;
        /**
         * total_retry_count stores the total number of times the tunneler has been retried by tunManager.
         */
        totalRetryCount?: number | null;
    }
    export class Resource$Organizations {
        context: APIRequestContext;
        locations: Resource$Organizations$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations {
        context: APIRequestContext;
        global: Resource$Organizations$Locations$Global;
        insights: Resource$Organizations$Locations$Insights;
        operations: Resource$Organizations$Locations$Operations;
        subscriptions: Resource$Organizations$Locations$Subscriptions;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations$Global {
        context: APIRequestContext;
        partnerTenants: Resource$Organizations$Locations$Global$Partnertenants;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations$Global$Partnertenants {
        context: APIRequestContext;
        browserDlpRules: Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules;
        proxyConfigs: Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs;
        constructor(context: APIRequestContext);
        /**
         * Deletes a single PartnerTenant.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single PartnerTenant.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant>>;
        get(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant>): void;
        get(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant>): void;
        /**
         * Lists PartnerTenants in a given organization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Global$Partnertenants$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Global$Partnertenants$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Global$Partnertenants$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Global$Partnertenants$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$Delete extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$Get extends StandardParameters {
        /**
         * Required. The resource name of the PartnerTenant using the form: `organizations/{organization_id\}/locations/global/partnerTenants/{partner_tenant_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation. All fields in the PartnerTenant message are supported. For example, the following query will return the PartnerTenants with displayName "test-tenant" organizations/${ORG_ID\}/locations/${LOCATION\}/partnerTenants?filter=displayName="test-tenant" Nested fields are also supported. The follow query will return PartnerTenants with internal_tenant_id "1234" organizations/${ORG_ID\}/locations/${LOCATION\}/partnerTenants?filter=partnerMetadata.internalTenantId="1234" For more information, please refer to https://google.aip.dev/160.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListPartnerTenantsResponse, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent organization to which the PartnerTenants belong. Format: `organizations/{organization_id\}/locations/global`
         */
        parent?: string;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$Browserdlprules$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Global$Partnertenants$Proxyconfigs$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Organizations$Locations$Insights {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        configuredInsight(params: Params$Resource$Organizations$Locations$Insights$Configuredinsight, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        configuredInsight(params?: Params$Resource$Organizations$Locations$Insights$Configuredinsight, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>>;
        configuredInsight(params: Params$Resource$Organizations$Locations$Insights$Configuredinsight, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        configuredInsight(params: Params$Resource$Organizations$Locations$Insights$Configuredinsight, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        configuredInsight(params: Params$Resource$Organizations$Locations$Insights$Configuredinsight, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        configuredInsight(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        /**
         * Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Insights$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Insights$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>>;
        get(params: Params$Resource$Organizations$Locations$Insights$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Insights$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        get(params: Params$Resource$Organizations$Locations$Insights$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        /**
         * Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Insights$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Insights$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Insights$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Insights$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Insights$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Insights$Configuredinsight extends StandardParameters {
        /**
         * Required. Aggregation type. Available aggregation could be fetched by calling insight list and get APIs in `BASIC` view.
         */
        aggregation?: string;
        /**
         * Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        'customGrouping.fieldFilter'?: string;
        /**
         * Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.
         */
        'customGrouping.groupFields'?: string[];
        /**
         * Required. Ending time for the duration for which insight is to be pulled.
         */
        endTime?: string;
        /**
         * Optional. Other filterable/configurable parameters as applicable to the selected insight. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        fieldFilter?: string;
        /**
         * Optional. Group id of the available groupings for the insight. Available groupings could be fetched by calling insight list and get APIs in `BASIC` view.
         */
        group?: string;
        /**
         * Required. The resource name of the insight using the form: `organizations/{organization_id\}/locations/{location_id\}/insights/{insight_id\}` `projects/{project_id\}/locations/{location_id\}/insights/{insight_id\}`.
         */
        insight?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. Used to fetch the page represented by the token. Fetches the first page when not set.
         */
        pageToken?: string;
        /**
         * Required. Starting time for the duration for which insight is to be pulled.
         */
        startTime?: string;
    }
    export interface Params$Resource$Organizations$Locations$Insights$Get extends StandardParameters {
        /**
         * Required. The resource name of the insight using the form: `organizations/{organization_id\}/locations/{location_id\}/insights/{insight_id\}` `projects/{project_id\}/locations/{location_id\}/insights/{insight_id\}`
         */
        name?: string;
        /**
         * Required. Metadata only or full data view.
         */
        view?: string;
    }
    export interface Params$Resource$Organizations$Locations$Insights$List extends StandardParameters {
        /**
         * Optional. Aggregation type. The default is 'DAILY'.
         */
        aggregation?: string;
        /**
         * Optional. Ending time for the duration for which insights are to be pulled. The default is the current time.
         */
        endTime?: string;
        /**
         * Optional. Filter expression to restrict the insights returned. Supported filter fields: * `type` * `category` * `subCategory` Examples: * "category = application AND type = count" * "category = application AND subCategory = iap" * "type = status" Allowed values: * type: [count, latency, status, list] * category: [application, device, request, security] * subCategory: [iap, caa, webprotect] NOTE: Only equality based comparison is allowed. Only `AND` conjunction is allowed. NOTE: The 'AND' in the filter field needs to be in capital letters only. NOTE: Just filtering on `subCategory` is not allowed. It should be passed in with the parent `category` too. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * Optional. Hint for how to order the results. This is currently ignored.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. NOTE: Default page size is 50.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The resource name of InsightMetadata using the form: `organizations/{organization_id\}/locations/{location\}` `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. Starting time for the duration for which insights are to be pulled. The default is 7 days before the current time.
         */
        startTime?: string;
        /**
         * Required. List only metadata or full data.
         */
        view?: string;
    }
    export class Resource$Organizations$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Organizations$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningListOperationsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleLongrunningCancelOperationRequest;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Organizations$Locations$Subscriptions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Cancels an existing BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization. Returns the timestamp for when the cancellation will become effective
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Organizations$Locations$Subscriptions$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Organizations$Locations$Subscriptions$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse>>;
        cancel(params: Params$Resource$Organizations$Locations$Subscriptions$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Organizations$Locations$Subscriptions$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse>): void;
        cancel(params: Params$Resource$Organizations$Locations$Subscriptions$Cancel, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse>): void;
        /**
         * Creates a new BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Subscriptions$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Organizations$Locations$Subscriptions$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>>;
        create(params: Params$Resource$Organizations$Locations$Subscriptions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Subscriptions$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        create(params: Params$Resource$Organizations$Locations$Subscriptions$Create, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        create(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        /**
         * Gets details of a single Subscription.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Subscriptions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Subscriptions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>>;
        get(params: Params$Resource$Organizations$Locations$Subscriptions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Subscriptions$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        get(params: Params$Resource$Organizations$Locations$Subscriptions$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        /**
         * Lists Subscriptions in a given organization and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Subscriptions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Subscriptions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Subscriptions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Subscriptions$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Subscriptions$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse>): void;
        /**
         * Updates an existing BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Organizations$Locations$Subscriptions$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Organizations$Locations$Subscriptions$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>>;
        patch(params: Params$Resource$Organizations$Locations$Subscriptions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Organizations$Locations$Subscriptions$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        patch(params: Params$Resource$Organizations$Locations$Subscriptions$Patch, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription>): void;
        /**
         * Restarts an existing BeyondCorp Enterprise Subscription in a given organization, that is scheduled for cancellation. Location will always be global as BeyondCorp subscriptions are per organization. Returns the timestamp for when the cancellation will become effective
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        restart(params: Params$Resource$Organizations$Locations$Subscriptions$Restart, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        restart(params?: Params$Resource$Organizations$Locations$Subscriptions$Restart, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse>>;
        restart(params: Params$Resource$Organizations$Locations$Subscriptions$Restart, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        restart(params: Params$Resource$Organizations$Locations$Subscriptions$Restart, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse>): void;
        restart(params: Params$Resource$Organizations$Locations$Subscriptions$Restart, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse>): void;
        restart(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$Cancel extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$Create extends StandardParameters {
        /**
         * Required. The resource name of the subscription location using the form: `organizations/{organization_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$Get extends StandardParameters {
        /**
         * Required. The resource name of Subscription using the form: `organizations/{organization_id\}/locations/{location\}/subscriptions/{subscription_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$List extends StandardParameters {
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListSubscriptionsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of Subscription using the form: `organizations/{organization_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$Patch extends StandardParameters {
        /**
         * Identifier. Unique resource name of the Subscription. The name is ignored when creating a subscription.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the Subscription resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. Mutable fields: seat_count.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription;
    }
    export interface Params$Resource$Organizations$Locations$Subscriptions$Restart extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        appConnections: Resource$Projects$Locations$Appconnections;
        appConnectors: Resource$Projects$Locations$Appconnectors;
        appGateways: Resource$Projects$Locations$Appgateways;
        applicationDomains: Resource$Projects$Locations$Applicationdomains;
        applications: Resource$Projects$Locations$Applications;
        clientConnectorServices: Resource$Projects$Locations$Clientconnectorservices;
        clientGateways: Resource$Projects$Locations$Clientgateways;
        connections: Resource$Projects$Locations$Connections;
        connectors: Resource$Projects$Locations$Connectors;
        global: Resource$Projects$Locations$Global;
        insights: Resource$Projects$Locations$Insights;
        operations: Resource$Projects$Locations$Operations;
        securityGateways: Resource$Projects$Locations$Securitygateways;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudLocationLocation>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudLocationLocation>, callback: BodyResponseCallback<Schema$GoogleCloudLocationLocation>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$GoogleCloudLocationLocation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudLocationLocation>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudLocationListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudLocationListLocationsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudLocationListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$GoogleCloudLocationListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudLocationListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Appconnections {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new AppConnection in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Appconnections$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Appconnections$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Appconnections$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Appconnections$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Appconnections$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single AppConnection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Appconnections$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Appconnections$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Appconnections$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Appconnections$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Appconnections$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single AppConnection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Appconnections$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Appconnections$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection>>;
        get(params: Params$Resource$Projects$Locations$Appconnections$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Appconnections$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection>): void;
        get(params: Params$Resource$Projects$Locations$Appconnections$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Appconnections$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists AppConnections in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Appconnections$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Appconnections$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse>>;
        list(params: Params$Resource$Projects$Locations$Appconnections$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Appconnections$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Appconnections$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse>): void;
        /**
         * Updates the parameters of a single AppConnection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Appconnections$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Appconnections$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Appconnections$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Appconnections$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Appconnections$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Resolves AppConnections details for a given AppConnector. An internal method called by a connector to find AppConnections to connect to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resolve(params: Params$Resource$Projects$Locations$Appconnections$Resolve, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resolve(params?: Params$Resource$Projects$Locations$Appconnections$Resolve, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse>>;
        resolve(params: Params$Resource$Projects$Locations$Appconnections$Resolve, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resolve(params: Params$Resource$Projects$Locations$Appconnections$Resolve, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse>): void;
        resolve(params: Params$Resource$Projects$Locations$Appconnections$Resolve, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse>): void;
        resolve(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Appconnections$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnections$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnections$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Appconnections$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnections$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnections$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnections$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Create extends StandardParameters {
        /**
         * Optional. User-settable AppConnection resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.
         */
        appConnectionId?: string;
        /**
         * Required. The resource project name of the AppConnection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/appConnections/{app_connection_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Get extends StandardParameters {
        /**
         * Required. BeyondCorp AppConnection name using the form: `projects/{project_id\}/locations/{location_id\}/appConnections/{app_connection_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListAppConnectionsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the AppConnection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Patch extends StandardParameters {
        /**
         * Optional. If set as true, will create the resource if it is not found.
         */
        allowMissing?: boolean;
        /**
         * Required. Unique resource name of the AppConnection. The name is ignored when creating a AppConnection.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.AppConnection]: * `labels` * `display_name` * `application_endpoint` * `connectors`
         */
        updateMask?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Resolve extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name of the connector associated with those AppConnections using the form: `projects/{project_id\}/locations/{location_id\}/appConnectors/{app_connector_id\}`
         */
        appConnectorId?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ResolveAppConnectionsResponse, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the AppConnection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Appconnections$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Appconnectors {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new AppConnector in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Appconnectors$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Appconnectors$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Appconnectors$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Appconnectors$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Appconnectors$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single AppConnector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Appconnectors$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Appconnectors$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Appconnectors$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Appconnectors$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Appconnectors$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single AppConnector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Appconnectors$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Appconnectors$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector>>;
        get(params: Params$Resource$Projects$Locations$Appconnectors$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Appconnectors$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector>): void;
        get(params: Params$Resource$Projects$Locations$Appconnectors$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Appconnectors$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists AppConnectors in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Appconnectors$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Appconnectors$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse>>;
        list(params: Params$Resource$Projects$Locations$Appconnectors$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Appconnectors$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Appconnectors$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse>): void;
        /**
         * Updates the parameters of a single AppConnector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Appconnectors$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Appconnectors$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Appconnectors$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Appconnectors$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Appconnectors$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Report status for a given connector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reportStatus(params: Params$Resource$Projects$Locations$Appconnectors$Reportstatus, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        reportStatus(params?: Params$Resource$Projects$Locations$Appconnectors$Reportstatus, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        reportStatus(params: Params$Resource$Projects$Locations$Appconnectors$Reportstatus, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reportStatus(params: Params$Resource$Projects$Locations$Appconnectors$Reportstatus, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        reportStatus(params: Params$Resource$Projects$Locations$Appconnectors$Reportstatus, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        reportStatus(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets instance configuration for a given AppConnector. An internal method called by a AppConnector to get its container config.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resolveInstanceConfig(params?: Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse>>;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse>): void;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse>): void;
        resolveInstanceConfig(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Appconnectors$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appconnectors$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnectors$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Appconnectors$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnectors$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnectors$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appconnectors$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Create extends StandardParameters {
        /**
         * Optional. User-settable AppConnector resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.
         */
        appConnectorId?: string;
        /**
         * Required. The resource project name of the AppConnector location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp AppConnector name using the form: `projects/{project_id\}/locations/{location_id\}/appConnectors/{app_connector_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Get extends StandardParameters {
        /**
         * Required. BeyondCorp AppConnector name using the form: `projects/{project_id\}/locations/{location_id\}/appConnectors/{app_connector_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListAppConnectorsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the AppConnector location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Patch extends StandardParameters {
        /**
         * Required. Unique resource name of the AppConnector. The name is ignored when creating a AppConnector.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.AppConnector]: * `labels` * `display_name`
         */
        updateMask?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Reportstatus extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector\}`
         */
        appConnector?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Resolveinstanceconfig extends StandardParameters {
        /**
         * Required. BeyondCorp AppConnector name using the form: `projects/{project_id\}/locations/{location_id\}/appConnectors/{app_connector\}`
         */
        appConnector?: string;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Appconnectors$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Appgateways {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new AppGateway in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Appgateways$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Appgateways$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Appgateways$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Appgateways$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Appgateways$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single AppGateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Appgateways$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Appgateways$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Appgateways$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Appgateways$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Appgateways$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single AppGateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Appgateways$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Appgateways$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AppGateway>>;
        get(params: Params$Resource$Projects$Locations$Appgateways$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Appgateways$Get, options: MethodOptions | BodyResponseCallback<Schema$AppGateway>, callback: BodyResponseCallback<Schema$AppGateway>): void;
        get(params: Params$Resource$Projects$Locations$Appgateways$Get, callback: BodyResponseCallback<Schema$AppGateway>): void;
        get(callback: BodyResponseCallback<Schema$AppGateway>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Appgateways$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists AppGateways in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Appgateways$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Appgateways$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAppGatewaysResponse>>;
        list(params: Params$Resource$Projects$Locations$Appgateways$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Appgateways$List, options: MethodOptions | BodyResponseCallback<Schema$ListAppGatewaysResponse>, callback: BodyResponseCallback<Schema$ListAppGatewaysResponse>): void;
        list(params: Params$Resource$Projects$Locations$Appgateways$List, callback: BodyResponseCallback<Schema$ListAppGatewaysResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAppGatewaysResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Appgateways$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Appgateways$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Appgateways$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Appgateways$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appgateways$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appgateways$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Appgateways$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Create extends StandardParameters {
        /**
         * Optional. User-settable AppGateway resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.
         */
        appGatewayId?: string;
        /**
         * Required. The resource project name of the AppGateway location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AppGateway;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp AppGateway name using the form: `projects/{project_id\}/locations/{location_id\}/appGateways/{app_gateway_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Get extends StandardParameters {
        /**
         * Required. BeyondCorp AppGateway name using the form: `projects/{project_id\}/locations/{location_id\}/appGateways/{app_gateway_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListAppGatewaysRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the AppGateway location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Appgateways$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Applicationdomains {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Applicationdomains$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Applicationdomains$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Applicationdomains$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Applications {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Applications$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Applications$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applications$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applications$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Applications$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Applications$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Applications$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applications$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applications$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Applications$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Applications$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Applications$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applications$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applications$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Applications$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Applications$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Applications$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Clientconnectorservices {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Clientconnectorservices$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Clientconnectorservices$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Clientconnectorservices$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Clientgateways {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientgateways$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Clientgateways$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientgateways$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientgateways$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clientgateways$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientgateways$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Clientgateways$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientgateways$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientgateways$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clientgateways$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Clientgateways$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Clientgateways$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Connections {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Connection in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Connections$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Connections$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Connections$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Connections$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Connections$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single Connection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Connections$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Connections$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Connections$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Connections$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Connections$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single Connection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Connections$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Connections$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Connection>>;
        get(params: Params$Resource$Projects$Locations$Connections$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Connections$Get, options: MethodOptions | BodyResponseCallback<Schema$Connection>, callback: BodyResponseCallback<Schema$Connection>): void;
        get(params: Params$Resource$Projects$Locations$Connections$Get, callback: BodyResponseCallback<Schema$Connection>): void;
        get(callback: BodyResponseCallback<Schema$Connection>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Connections$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Connections$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connections$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connections$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connections$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists Connections in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Connections$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Connections$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListConnectionsResponse>>;
        list(params: Params$Resource$Projects$Locations$Connections$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Connections$List, options: MethodOptions | BodyResponseCallback<Schema$ListConnectionsResponse>, callback: BodyResponseCallback<Schema$ListConnectionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Connections$List, callback: BodyResponseCallback<Schema$ListConnectionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListConnectionsResponse>): void;
        /**
         * Updates the parameters of a single Connection.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Connections$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Connections$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Connections$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Connections$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Connections$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Resolves connections details for a given connector. An internal method called by a connector to find connections to connect to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resolve(params: Params$Resource$Projects$Locations$Connections$Resolve, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resolve(params?: Params$Resource$Projects$Locations$Connections$Resolve, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ResolveConnectionsResponse>>;
        resolve(params: Params$Resource$Projects$Locations$Connections$Resolve, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resolve(params: Params$Resource$Projects$Locations$Connections$Resolve, options: MethodOptions | BodyResponseCallback<Schema$ResolveConnectionsResponse>, callback: BodyResponseCallback<Schema$ResolveConnectionsResponse>): void;
        resolve(params: Params$Resource$Projects$Locations$Connections$Resolve, callback: BodyResponseCallback<Schema$ResolveConnectionsResponse>): void;
        resolve(callback: BodyResponseCallback<Schema$ResolveConnectionsResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Connections$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Connections$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connections$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connections$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connections$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
    }
    export interface Params$Resource$Projects$Locations$Connections$Create extends StandardParameters {
        /**
         * Optional. User-settable connection resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.
         */
        connectionId?: string;
        /**
         * Required. The resource project name of the connection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Connection;
    }
    export interface Params$Resource$Projects$Locations$Connections$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connections/{connection_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Connections$Get extends StandardParameters {
        /**
         * Required. BeyondCorp Connection name using the form: `projects/{project_id\}/locations/{location_id\}/connections/{connection_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Connections$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Connections$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListConnectionsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the connection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Connections$Patch extends StandardParameters {
        /**
         * Optional. If set as true, will create the resource if it is not found.
         */
        allowMissing?: boolean;
        /**
         * Required. Unique resource name of the connection. The name is ignored when creating a connection.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.Connection]: * `labels` * `display_name` * `application_endpoint` * `connectors`
         */
        updateMask?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Connection;
    }
    export interface Params$Resource$Projects$Locations$Connections$Resolve extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name of the connector associated with those connections using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector_id\}`
         */
        connectorId?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ResolveConnectionsResponse, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the connection location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Connections$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export class Resource$Projects$Locations$Connectors {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Connector in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Connectors$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Connectors$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Connectors$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Connectors$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Connectors$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single Connector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Connectors$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Connectors$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Connectors$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Connectors$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Connectors$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single Connector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Connectors$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Connectors$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Connector>>;
        get(params: Params$Resource$Projects$Locations$Connectors$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Connectors$Get, options: MethodOptions | BodyResponseCallback<Schema$Connector>, callback: BodyResponseCallback<Schema$Connector>): void;
        get(params: Params$Resource$Projects$Locations$Connectors$Get, callback: BodyResponseCallback<Schema$Connector>): void;
        get(callback: BodyResponseCallback<Schema$Connector>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Connectors$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists Connectors in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Connectors$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Connectors$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListConnectorsResponse>>;
        list(params: Params$Resource$Projects$Locations$Connectors$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Connectors$List, options: MethodOptions | BodyResponseCallback<Schema$ListConnectorsResponse>, callback: BodyResponseCallback<Schema$ListConnectorsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Connectors$List, callback: BodyResponseCallback<Schema$ListConnectorsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListConnectorsResponse>): void;
        /**
         * Updates the parameters of a single Connector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Connectors$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Connectors$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Connectors$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Connectors$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Connectors$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Report status for a given connector.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reportStatus(params: Params$Resource$Projects$Locations$Connectors$Reportstatus, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        reportStatus(params?: Params$Resource$Projects$Locations$Connectors$Reportstatus, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        reportStatus(params: Params$Resource$Projects$Locations$Connectors$Reportstatus, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reportStatus(params: Params$Resource$Projects$Locations$Connectors$Reportstatus, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        reportStatus(params: Params$Resource$Projects$Locations$Connectors$Reportstatus, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        reportStatus(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets instance configuration for a given connector. An internal method called by a connector to get its container config.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resolveInstanceConfig(params?: Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ResolveInstanceConfigResponse>>;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig, options: MethodOptions | BodyResponseCallback<Schema$ResolveInstanceConfigResponse>, callback: BodyResponseCallback<Schema$ResolveInstanceConfigResponse>): void;
        resolveInstanceConfig(params: Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig, callback: BodyResponseCallback<Schema$ResolveInstanceConfigResponse>): void;
        resolveInstanceConfig(callback: BodyResponseCallback<Schema$ResolveInstanceConfigResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Connectors$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Connectors$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Create extends StandardParameters {
        /**
         * Optional. User-settable connector resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.
         */
        connectorId?: string;
        /**
         * Required. The resource project name of the connector location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Connector;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Get extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Connectors$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListConnectorsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the connector location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Patch extends StandardParameters {
        /**
         * Required. Unique resource name of the connector. The name is ignored when creating a connector.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.Connector]: * `labels` * `display_name`
         */
        updateMask?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Connector;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Reportstatus extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector\}`
         */
        connector?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReportStatusRequest;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Resolveinstanceconfig extends StandardParameters {
        /**
         * Required. BeyondCorp Connector name using the form: `projects/{project_id\}/locations/{location_id\}/connectors/{connector\}`
         */
        connector?: string;
    }
    export interface Params$Resource$Projects$Locations$Connectors$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export class Resource$Projects$Locations$Global {
        context: APIRequestContext;
        securityGateways: Resource$Projects$Locations$Global$Securitygateways;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Global$Securitygateways {
        context: APIRequestContext;
        applications: Resource$Projects$Locations$Global$Securitygateways$Applications;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Global$Securitygateways$Applications {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Application in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Updates the parameters of a single Application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Create extends StandardParameters {
        /**
         * Optional. User-settable Application resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or letter.
         */
        applicationId?: string;
        /**
         * Required. The resource name of the parent SecurityGateway using the form: `projects/{project_id\}/locations/global/securityGateways/{security_gateway_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication;
    }
    export interface Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Patch extends StandardParameters {
        /**
         * Identifier. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request timed out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. Mutable fields include: display_name.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication;
    }
    export interface Params$Resource$Projects$Locations$Global$Securitygateways$Applications$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Insights {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        configuredInsight(params: Params$Resource$Projects$Locations$Insights$Configuredinsight, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        configuredInsight(params?: Params$Resource$Projects$Locations$Insights$Configuredinsight, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>>;
        configuredInsight(params: Params$Resource$Projects$Locations$Insights$Configuredinsight, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        configuredInsight(params: Params$Resource$Projects$Locations$Insights$Configuredinsight, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        configuredInsight(params: Params$Resource$Projects$Locations$Insights$Configuredinsight, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        configuredInsight(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse>): void;
        /**
         * Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Insights$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Insights$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>>;
        get(params: Params$Resource$Projects$Locations$Insights$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Insights$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        get(params: Params$Resource$Projects$Locations$Insights$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight>): void;
        /**
         * Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Insights$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Insights$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>>;
        list(params: Params$Resource$Projects$Locations$Insights$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Insights$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Insights$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Insights$Configuredinsight extends StandardParameters {
        /**
         * Required. Aggregation type. Available aggregation could be fetched by calling insight list and get APIs in `BASIC` view.
         */
        aggregation?: string;
        /**
         * Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        'customGrouping.fieldFilter'?: string;
        /**
         * Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.
         */
        'customGrouping.groupFields'?: string[];
        /**
         * Required. Ending time for the duration for which insight is to be pulled.
         */
        endTime?: string;
        /**
         * Optional. Other filterable/configurable parameters as applicable to the selected insight. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter="fieldName1=fieldVal1 AND fieldName2=fieldVal2". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        fieldFilter?: string;
        /**
         * Optional. Group id of the available groupings for the insight. Available groupings could be fetched by calling insight list and get APIs in `BASIC` view.
         */
        group?: string;
        /**
         * Required. The resource name of the insight using the form: `organizations/{organization_id\}/locations/{location_id\}/insights/{insight_id\}` `projects/{project_id\}/locations/{location_id\}/insights/{insight_id\}`.
         */
        insight?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. Used to fetch the page represented by the token. Fetches the first page when not set.
         */
        pageToken?: string;
        /**
         * Required. Starting time for the duration for which insight is to be pulled.
         */
        startTime?: string;
    }
    export interface Params$Resource$Projects$Locations$Insights$Get extends StandardParameters {
        /**
         * Required. The resource name of the insight using the form: `organizations/{organization_id\}/locations/{location_id\}/insights/{insight_id\}` `projects/{project_id\}/locations/{location_id\}/insights/{insight_id\}`
         */
        name?: string;
        /**
         * Required. Metadata only or full data view.
         */
        view?: string;
    }
    export interface Params$Resource$Projects$Locations$Insights$List extends StandardParameters {
        /**
         * Optional. Aggregation type. The default is 'DAILY'.
         */
        aggregation?: string;
        /**
         * Optional. Ending time for the duration for which insights are to be pulled. The default is the current time.
         */
        endTime?: string;
        /**
         * Optional. Filter expression to restrict the insights returned. Supported filter fields: * `type` * `category` * `subCategory` Examples: * "category = application AND type = count" * "category = application AND subCategory = iap" * "type = status" Allowed values: * type: [count, latency, status, list] * category: [application, device, request, security] * subCategory: [iap, caa, webprotect] NOTE: Only equality based comparison is allowed. Only `AND` conjunction is allowed. NOTE: The 'AND' in the filter field needs to be in capital letters only. NOTE: Just filtering on `subCategory` is not allowed. It should be passed in with the parent `category` too. (These expressions are based on the filter language described at https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * Optional. Hint for how to order the results. This is currently ignored.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. NOTE: Default page size is 50.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The resource name of InsightMetadata using the form: `organizations/{organization_id\}/locations/{location\}` `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. Starting time for the duration for which insights are to be pulled. The default is 7 days before the current time.
         */
        startTime?: string;
        /**
         * Required. List only metadata or full data.
         */
        view?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleLongrunningCancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Securitygateways {
        context: APIRequestContext;
        applications: Resource$Projects$Locations$Securitygateways$Applications;
        constructor(context: APIRequestContext);
        /**
         * Creates a new SecurityGateway in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Securitygateways$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Securitygateways$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        create(params: Params$Resource$Projects$Locations$Securitygateways$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Securitygateways$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Securitygateways$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Deletes a single SecurityGateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Securitygateways$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single SecurityGateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Securitygateways$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Securitygateways$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway>>;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway>): void;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Securitygateways$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists SecurityGateways in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Securitygateways$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Securitygateways$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse>>;
        list(params: Params$Resource$Projects$Locations$Securitygateways$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Securitygateways$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse>): void;
        list(params: Params$Resource$Projects$Locations$Securitygateways$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse>): void;
        /**
         * Updates the parameters of a single SecurityGateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Securitygateways$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Securitygateways$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        patch(params: Params$Resource$Projects$Locations$Securitygateways$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Securitygateways$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Securitygateways$Patch, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Securitygateways$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Securitygateways$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Securitygateways$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Securitygateways$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Securitygateways$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Securitygateways$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Create extends StandardParameters {
        /**
         * Required. The resource project name of the SecurityGateway location using the form: `projects/{project_id\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request.
         */
        requestId?: string;
        /**
         * Optional. User-settable SecurityGateway resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or letter.
         */
        securityGatewayId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Delete extends StandardParameters {
        /**
         * Required. BeyondCorp SecurityGateway name using the form: `projects/{project_id\}/locations/{location_id\}/securityGateways/{security_gateway_id\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Get extends StandardParameters {
        /**
         * Required. The resource name of the PartnerTenant using the form: `projects/{project_id\}/locations/{location_id\}/securityGateway/{security_gateway_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation. All fields in the SecurityGateway message are supported. For example, the following query will return the SecurityGateway with displayName "test-security-gateway" For more information, please refer to https://google.aip.dev/160.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListSecurityGatewayRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent location to which the resources belong. `projects/{project_id\}/locations/{location_id\}/`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Patch extends StandardParameters {
        /**
         * Identifier. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request timed out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. Mutable fields include: display_name, hubs.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Securitygateways$Applications {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Deletes a single Application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Securitygateways$Applications$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Delete, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets details of a single Application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Securitygateways$Applications$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication>>;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication>): void;
        get(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Get, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists Applications in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Securitygateways$Applications$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Securitygateways$Applications$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Securitygateways$Applications$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Securitygateways$Applications$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Securitygateways$Applications$List, callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Applications$Delete extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.
         */
        validateOnly?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Applications$Get extends StandardParameters {
        /**
         * Required. The resource name of the Application using the form: `projects/{project_id\}/locations/global/securityGateway/{security_gateway_id\}/applications/{application_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Applications$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Applications$List extends StandardParameters {
        /**
         * Optional. A filter specifying constraints of a list operation. All fields in the Application message are supported. For example, the following query will return the Application with displayName "test-application" For more information, please refer to https://google.aip.dev/160.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous ListApplicationsRequest, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent location to which the resources belong. `projects/{project_id\}/locations/global/securityGateways/{security_gateway_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Securitygateways$Applications$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export {};
}
