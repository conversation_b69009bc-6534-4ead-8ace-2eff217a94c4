const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function checkCurrentState() {
  console.log('🔍 Current Migration State Check');
  console.log('================================');
  console.log('');

  try {
    // Check database records
    console.log('📊 Database Analysis:');
    console.log('---------------------');
    
    const { data: allRecords, error: allError } = await supabase
      .from('airtable_data')
      .select('id, name, company, pdf_url, pdf')
      .not('pdf', 'is', null);

    if (allError) {
      console.error('❌ Error fetching records:', allError);
      return;
    }

    console.log(`📋 Total records with PDF data: ${allRecords.length}`);

    // Categorize records by PDF URL type
    let supabaseUrls = 0;
    let googleDriveUrls = 0;
    let airtableUrls = 0;
    let otherUrls = 0;
    let noUrls = 0;
    let hasOriginalData = 0;

    for (const record of allRecords) {
      // Check if has original PDF data
      try {
        const pdfData = JSON.parse(record.pdf);
        if (pdfData && pdfData.length > 0 && pdfData[0].url) {
          hasOriginalData++;
        }
      } catch {}

      // Categorize current PDF URL
      if (!record.pdf_url) {
        noUrls++;
      } else if (record.pdf_url.includes('case-study-pdfs')) {
        supabaseUrls++;
      } else if (record.pdf_url.includes('drive.google.com')) {
        googleDriveUrls++;
      } else if (record.pdf_url.includes('dl.airtable.com')) {
        airtableUrls++;
      } else {
        otherUrls++;
      }
    }

    console.log(`   📄 Records with original Airtable data: ${hasOriginalData}`);
    console.log(`   🔗 Current PDF URL distribution:`);
    console.log(`      • Supabase URLs: ${supabaseUrls}`);
    console.log(`      • Google Drive URLs: ${googleDriveUrls}`);
    console.log(`      • Airtable URLs: ${airtableUrls}`);
    console.log(`      • Other URLs: ${otherUrls}`);
    console.log(`      • No URL: ${noUrls}`);
    console.log('');

    // Check storage usage
    console.log('💾 Storage Analysis:');
    console.log('--------------------');
    
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
      return;
    }

    let totalStorage = 0;

    for (const bucket of buckets) {
      const { data: files, error: filesError } = await supabase.storage
        .from(bucket.name)
        .list('', { limit: 1000 });

      if (filesError) {
        console.error(`❌ Error listing files in ${bucket.name}:`, filesError);
        continue;
      }

      let bucketSize = 0;
      for (const file of files) {
        if (file.metadata && file.metadata.size) {
          bucketSize += file.metadata.size;
        }
      }

      totalStorage += bucketSize;
      console.log(`   🪣 ${bucket.name}: ${files.length} files, ${formatBytes(bucketSize)}`);
    }

    console.log(`   📊 Total Supabase storage: ${formatBytes(totalStorage)}`);
    console.log(`   🎯 Free tier limit: ${formatBytes(1024 * 1024 * 1024)} (1GB)`);
    console.log(`   📈 Usage: ${((totalStorage / (1024 * 1024 * 1024)) * 100).toFixed(1)}%`);
    console.log(`   ✅ Within limit: ${totalStorage < 1024 * 1024 * 1024 ? 'YES' : 'NO'}`);
    console.log('');

    // Migration recommendations
    console.log('💡 Migration Status:');
    console.log('--------------------');
    
    if (supabaseUrls > 0) {
      console.log(`⚠️  ${supabaseUrls} records still point to empty Supabase bucket`);
      console.log('   → These need to be migrated to Google Drive');
    }
    
    if (hasOriginalData > googleDriveUrls) {
      console.log(`🔄 ${hasOriginalData - googleDriveUrls} records can be migrated to Google Drive`);
      console.log('   → Run: node migrate-from-airtable-to-drive.js');
    }
    
    if (googleDriveUrls === hasOriginalData) {
      console.log('🎉 All records with PDF data are using Google Drive!');
    }

    console.log('');
    console.log('📋 Next Steps:');
    if (supabaseUrls > 0 || googleDriveUrls < hasOriginalData) {
      console.log('1. Set up Google Drive API credentials');
      console.log('2. Run: node test-google-drive.js (to test setup)');
      console.log('3. Run: node migrate-from-airtable-to-drive.js (to migrate)');
    } else {
      console.log('✅ Migration appears complete!');
      console.log('1. Verify Google Drive links are working');
      console.log('2. Consider deleting empty case-study-pdfs bucket');
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

checkCurrentState();
