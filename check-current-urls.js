const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkCurrentState() {
  console.log('🔍 Checking current database state...');
  console.log('');

  // Get sample records
  const { data: records, error } = await supabase
    .from('airtable_data')
    .select('id, name, company, pdf_url, pdf')
    .limit(5);

  if (error) {
    console.error('❌ Database error:', error.message);
    return;
  }

  console.log('📊 Sample records:');
  console.log('');

  records.forEach((record, i) => {
    console.log(`${i + 1}. ${record.name} (${record.company})`);
    console.log(`   📄 PDF URL: ${record.pdf_url}`);
    
    // Check if it's Airtable URL
    if (record.pdf_url && record.pdf_url.includes('airtableusercontent.com')) {
      console.log('   🔗 Type: Airtable URL (may be expired)');
    } else if (record.pdf_url && record.pdf_url.includes('supabase.co')) {
      console.log('   🔗 Type: Supabase URL');
    } else if (record.pdf_url && record.pdf_url.includes('drive.google.com')) {
      console.log('   🔗 Type: Google Drive URL');
    } else {
      console.log('   🔗 Type: Unknown');
    }
    
    // Check PDF data
    if (record.pdf) {
      try {
        const pdfData = JSON.parse(record.pdf);
        if (pdfData && pdfData.length > 0 && pdfData[0].url) {
          console.log(`   📎 PDF Data URL: ${pdfData[0].url.substring(0, 80)}...`);
          if (pdfData[0].url.includes('airtableusercontent.com')) {
            console.log('   📎 PDF Data Type: Airtable URL');
          }
        }
      } catch (e) {
        console.log('   📎 PDF Data: Invalid JSON');
      }
    }
    console.log('');
  });

  // Get counts by URL type
  console.log('📈 URL Distribution:');
  console.log('');

  const { data: airtableCount } = await supabase
    .from('airtable_data')
    .select('id', { count: 'exact' })
    .like('pdf_url', '%airtableusercontent.com%');

  const { data: supabaseCount } = await supabase
    .from('airtable_data')
    .select('id', { count: 'exact' })
    .like('pdf_url', '%supabase.co%');

  const { data: driveCount } = await supabase
    .from('airtable_data')
    .select('id', { count: 'exact' })
    .like('pdf_url', '%drive.google.com%');

  const { data: totalCount } = await supabase
    .from('airtable_data')
    .select('id', { count: 'exact' })
    .not('pdf', 'is', null);

  console.log(`📊 Total records with PDFs: ${totalCount.length}`);
  console.log(`🔗 Airtable URLs: ${airtableCount.length}`);
  console.log(`💾 Supabase URLs: ${supabaseCount.length}`);
  console.log(`☁️  Google Drive URLs: ${driveCount.length}`);
  console.log('');

  if (airtableCount.length > 0) {
    console.log('⚠️  Airtable URLs detected - these may be expired!');
    console.log('💡 Need to migrate to Google Drive for production use');
  }
}

checkCurrentState().catch(console.error);
