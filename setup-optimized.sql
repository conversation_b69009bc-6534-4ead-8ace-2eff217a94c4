-- =====================================================
-- OPTIMIZED SUPABASE SETUP FOR AIRTABLE MIGRATION
-- =====================================================

-- 1. Create Companies Table
-- =====================================================
CREATE TABLE IF NOT EXISTS companies (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  logo_url TEXT,
  description TEXT,
  website TEXT,
  industry TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_slug ON companies(slug);

-- 2. Update airtable_data table to include company_id reference
-- =====================================================
ALTER TABLE airtable_data 
ADD COLUMN IF NOT EXISTS company_id INTEGER REFERENCES companies(id);

-- Add index for company relationship
CREATE INDEX IF NOT EXISTS idx_airtable_data_company_id ON airtable_data(company_id);

-- 3. Create Storage Buckets (Run these in Supabase Dashboard)
-- =====================================================
-- Note: These need to be created via Supabase Dashboard or API
-- Go to Storage > Create Bucket

-- Bucket 1: company-logos
-- - Name: company-logos
-- - Public: true (for easy access to logos)
-- - File size limit: 5MB
-- - Allowed MIME types: image/jpeg, image/png, image/svg+xml, image/webp

-- Bucket 2: case-study-pdfs  
-- - Name: case-study-pdfs
-- - Public: true (for easy access to PDFs)
-- - File size limit: 50MB
-- - Allowed MIME types: application/pdf

-- 4. Row Level Security (Optional but recommended)
-- =====================================================

-- Enable RLS on companies table
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

-- Policy: Allow read access to everyone
CREATE POLICY "Allow public read access on companies" 
ON companies FOR SELECT 
USING (true);

-- Policy: Allow authenticated users to insert/update
CREATE POLICY "Allow authenticated insert/update on companies" 
ON companies FOR ALL 
USING (auth.role() = 'authenticated');

-- Enable RLS on airtable_data table
ALTER TABLE airtable_data ENABLE ROW LEVEL SECURITY;

-- Policy: Allow read access to everyone
CREATE POLICY "Allow public read access on airtable_data" 
ON airtable_data FOR SELECT 
USING (true);

-- Policy: Allow authenticated users to insert/update
CREATE POLICY "Allow authenticated insert/update on airtable_data" 
ON airtable_data FOR ALL 
USING (auth.role() = 'authenticated');

-- 5. Useful Views for easier querying
-- =====================================================

-- View: Case studies with company information
CREATE OR REPLACE VIEW case_studies_with_companies AS
SELECT 
  ad.id,
  ad.name as case_study_name,
  ad.airtable_id,
  c.name as company_name,
  c.slug as company_slug,
  c.logo_url as company_logo,
  ad.organizer,
  ad.type_field,
  ad.objective,
  ad.category,
  ad.publish,
  ad.sort_field,
  ad.likes,
  ad.market,
  ad.pdf_url,
  ad.created_at,
  ad.updated_at
FROM airtable_data ad
LEFT JOIN companies c ON ad.company_id = c.id;

-- View: Companies with case study counts
CREATE OR REPLACE VIEW companies_with_stats AS
SELECT 
  c.*,
  COUNT(ad.id) as case_study_count,
  MAX(ad.created_at) as latest_case_study
FROM companies c
LEFT JOIN airtable_data ad ON c.id = ad.company_id
GROUP BY c.id, c.name, c.slug, c.logo_url, c.description, c.website, c.industry, c.created_at, c.updated_at;

-- 6. Functions for better data management
-- =====================================================

-- Function: Update company slug when name changes
CREATE OR REPLACE FUNCTION update_company_slug()
RETURNS TRIGGER AS $$
BEGIN
  NEW.slug = LOWER(REGEXP_REPLACE(NEW.name, '[^a-zA-Z0-9]+', '-', 'g'));
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger: Auto-update slug and timestamp
CREATE TRIGGER trigger_update_company_slug
  BEFORE UPDATE ON companies
  FOR EACH ROW
  EXECUTE FUNCTION update_company_slug();

-- Function: Update airtable_data timestamp
CREATE OR REPLACE FUNCTION update_airtable_data_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger: Auto-update timestamp
CREATE TRIGGER trigger_update_airtable_data_timestamp
  BEFORE UPDATE ON airtable_data
  FOR EACH ROW
  EXECUTE FUNCTION update_airtable_data_timestamp();

-- 7. Sample queries for testing
-- =====================================================

-- Get all case studies with company info
-- SELECT * FROM case_studies_with_companies ORDER BY created_at DESC;

-- Get companies with most case studies
-- SELECT * FROM companies_with_stats ORDER BY case_study_count DESC;

-- Search case studies by company
-- SELECT * FROM case_studies_with_companies WHERE company_name ILIKE '%google%';

-- Get case studies by category
-- SELECT name, company_name, category FROM case_studies_with_companies 
-- WHERE category::text ILIKE '%design%';

-- =====================================================
-- SETUP COMPLETE
-- =====================================================

-- Next steps:
-- 1. Run this SQL in Supabase SQL Editor
-- 2. Create storage buckets in Supabase Dashboard:
--    - company-logos (public, 5MB limit, images only)
--    - case-study-pdfs (public, 50MB limit, PDFs only)
-- 3. Run the optimized migration script: node migrate-optimized.js
