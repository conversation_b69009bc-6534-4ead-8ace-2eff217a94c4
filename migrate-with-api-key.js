const { createClient } = require('@supabase/supabase-js');
const https = require('https');
const fs = require('fs');
const FormData = require('form-data');
require('dotenv').config();

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

class GoogleDriveAPIKeyMigration {
  constructor() {
    this.apiKey = process.env.GOOGLE_DRIVE_API_KEY;
    this.folderId = process.env.GOOGLE_DRIVE_FOLDER_ID;
    this.stats = {
      totalRecords: 0,
      processed: 0,
      uploaded: 0,
      skipped: 0,
      errors: 0,
      totalSize: 0
    };
  }

  /**
   * Test API key access
   */
  async testAPIKey() {
    return new Promise((resolve) => {
      const url = `https://www.googleapis.com/drive/v3/about?key=${this.apiKey}&fields=user`;
      
      https.get(url, (response) => {
        let data = '';
        response.on('data', (chunk) => data += chunk);
        response.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (result.error) {
              console.log('❌ API Key test failed:', result.error.message);
              resolve(false);
            } else {
              console.log('✅ API Key is valid');
              if (result.user) {
                console.log(`   👤 User: ${result.user.displayName || 'API User'}`);
              }
              resolve(true);
            }
          } catch (error) {
            console.log('❌ API response parsing failed');
            resolve(false);
          }
        });
      }).on('error', () => {
        console.log('❌ API request failed');
        resolve(false);
      });
    });
  }

  /**
   * Alternative approach: Use Google Drive's resumable upload with API key
   * This is a workaround that might work for some scenarios
   */
  async uploadWithResumableAPI(fileName, fileBuffer) {
    return new Promise((resolve, reject) => {
      // Step 1: Initiate resumable upload session
      const metadata = {
        name: fileName,
        parents: [this.folderId]
      };

      const initData = JSON.stringify(metadata);
      
      const options = {
        hostname: 'www.googleapis.com',
        port: 443,
        path: `/upload/drive/v3/files?uploadType=resumable&key=${this.apiKey}`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': initData.length,
          'X-Upload-Content-Type': 'application/pdf',
          'X-Upload-Content-Length': fileBuffer.length
        }
      };

      const req = https.request(options, (res) => {
        if (res.statusCode === 200 && res.headers.location) {
          // Step 2: Upload the actual file data
          this.uploadFileData(res.headers.location, fileBuffer, resolve, reject);
        } else {
          let errorData = '';
          res.on('data', (chunk) => errorData += chunk);
          res.on('end', () => {
            try {
              const error = JSON.parse(errorData);
              reject(new Error(`Upload initiation failed: ${error.error?.message || 'Unknown error'}`));
            } catch (e) {
              reject(new Error(`Upload initiation failed: HTTP ${res.statusCode}`));
            }
          });
        }
      });

      req.on('error', reject);
      req.write(initData);
      req.end();
    });
  }

  /**
   * Upload file data to resumable upload URL
   */
  uploadFileData(uploadUrl, fileBuffer, resolve, reject) {
    const url = new URL(uploadUrl);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Length': fileBuffer.length
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const result = JSON.parse(responseData);
            resolve({
              id: result.id,
              name: result.name,
              viewLink: `https://drive.google.com/file/d/${result.id}/view`,
              downloadLink: `https://drive.google.com/uc?id=${result.id}`,
              size: fileBuffer.length
            });
          } catch (e) {
            reject(new Error('Failed to parse upload response'));
          }
        } else {
          reject(new Error(`File upload failed: HTTP ${res.statusCode}`));
        }
      });
    });

    req.on('error', reject);
    req.write(fileBuffer);
    req.end();
  }

  /**
   * Make file publicly accessible
   */
  async makeFilePublic(fileId) {
    return new Promise((resolve) => {
      const permissionData = JSON.stringify({
        role: 'reader',
        type: 'anyone'
      });

      const options = {
        hostname: 'www.googleapis.com',
        port: 443,
        path: `/drive/v3/files/${fileId}/permissions?key=${this.apiKey}`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': permissionData.length
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          resolve(res.statusCode === 200);
        });
      });

      req.on('error', () => resolve(false));
      req.write(permissionData);
      req.end();
    });
  }

  /**
   * Download file from URL
   */
  async downloadFile(url) {
    return new Promise((resolve, reject) => {
      const timeout = 30000;
      
      const request = https.get(url, { timeout }, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}`));
          return;
        }

        const chunks = [];
        let totalSize = 0;
        
        response.on('data', (chunk) => {
          chunks.push(chunk);
          totalSize += chunk.length;
          
          if (totalSize > 50 * 1024 * 1024) {
            response.destroy();
            reject(new Error('File too large'));
            return;
          }
        });
        
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      });
      
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Download timeout'));
      });
      
      request.on('error', reject);
    });
  }

  /**
   * Generate filename
   */
  generateFileName(company, originalName, timestamp) {
    const cleanCompany = company
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 30);

    const cleanOriginal = originalName
      .replace(/\.[^/.]+$/, '')
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50);

    return `${cleanCompany}-${cleanOriginal}-${timestamp}.pdf`;
  }

  /**
   * Format bytes
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Main migration function
   */
  async migrate() {
    console.log('🔑 Google Drive API Key Migration');
    console.log('==================================');
    console.log(`🔍 API Key: ${this.apiKey.substring(0, 10)}...`);
    console.log(`📁 Folder ID: ${this.folderId}`);
    console.log('');

    // Test API key
    console.log('🧪 Testing API key...');
    const apiWorks = await this.testAPIKey();
    
    if (!apiWorks) {
      console.log('❌ API key test failed - cannot proceed');
      return;
    }

    try {
      // Get records with Airtable URLs
      console.log('📊 Fetching records from database...');
      const { data: records, error } = await supabase
        .from('airtable_data')
        .select('id, name, company, pdf_url, pdf, created_at')
        .not('pdf', 'is', null)
        .like('pdf_url', '%airtableusercontent.com%')
        .order('created_at', { ascending: true })
        .limit(5); // Start with just 5 records to test

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      this.stats.totalRecords = records.length;
      console.log(`📋 Found ${records.length} records to migrate (testing with first 5)`);
      
      if (records.length === 0) {
        console.log('✅ No records need migration!');
        return;
      }
      
      console.log('');
      console.log('🔄 Starting migration...');
      console.log('');

      for (const record of records) {
        await this.migrateRecord(record);
        
        // Small delay
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Final results
      console.log('');
      console.log('🎉 TEST MIGRATION COMPLETE!');
      console.log('===========================');
      console.log(`📊 Records processed: ${this.stats.totalRecords}`);
      console.log(`✅ Successfully uploaded: ${this.stats.uploaded}`);
      console.log(`❌ Errors: ${this.stats.errors}`);
      console.log(`💾 Total uploaded: ${this.formatBytes(this.stats.totalSize)}`);
      
      if (this.stats.uploaded > 0) {
        console.log('');
        console.log('🚀 API key approach is working!');
        console.log('📋 To migrate all records:');
        console.log('   1. Edit this script and remove the .limit(5)');
        console.log('   2. Run the script again');
      }

    } catch (error) {
      console.error('💥 Migration failed:', error.message);
    }
  }

  /**
   * Migrate single record
   */
  async migrateRecord(record) {
    try {
      this.stats.processed++;
      
      console.log(`📄 ${this.stats.processed}/${this.stats.totalRecords}: ${record.name}`);
      
      // Parse PDF data
      let pdfData;
      try {
        pdfData = JSON.parse(record.pdf);
      } catch (parseError) {
        throw new Error('Could not parse PDF data');
      }

      if (!pdfData || pdfData.length === 0 || !pdfData[0].url) {
        throw new Error('No valid PDF URL found');
      }

      const airtableUrl = pdfData[0].url;
      const originalFilename = pdfData[0].filename || 'document.pdf';
      
      // Generate filename
      const timestamp = new Date(record.created_at).getTime();
      const driveFilename = this.generateFileName(record.company, originalFilename, timestamp);
      
      console.log(`   📥 Downloading from Airtable...`);
      const fileBuffer = await this.downloadFile(airtableUrl);
      
      console.log(`   📤 Uploading to Google Drive: ${this.formatBytes(fileBuffer.length)}`);
      const uploadResult = await this.uploadWithResumableAPI(driveFilename, fileBuffer);
      
      console.log(`   🔓 Making file public...`);
      await this.makeFilePublic(uploadResult.id);
      
      // Update database
      console.log(`   💾 Updating database...`);
      const { error: updateError } = await supabase
        .from('airtable_data')
        .update({ pdf_url: uploadResult.viewLink })
        .eq('id', record.id);
      
      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`);
      }
      
      this.stats.uploaded++;
      this.stats.totalSize += uploadResult.size;
      
      console.log(`   ✅ Success: ${uploadResult.viewLink}`);
      
    } catch (error) {
      this.stats.errors++;
      console.error(`   ❌ Failed: ${error.message}`);
    }
  }
}

// Run migration
if (require.main === module) {
  const migration = new GoogleDriveAPIKeyMigration();
  migration.migrate().catch(err => {
    console.error('💥 Critical error:', err.message);
    process.exit(1);
  });
}

module.exports = GoogleDriveAPIKeyMigration;
