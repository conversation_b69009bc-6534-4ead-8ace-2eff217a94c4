const Airtable = require("airtable");
const axios = require("axios");

// --- Configuration ---
// For better security, use environment variables (e.g., process.env.AIRTABLE_API_KEY)
// or a .env file (with the `dotenv` package) instead of hardcoding keys.

// Airtable Configuration
const AIRTABLE_API_KEY = 'YOUR_AIRTABLE_KEY';
const AIRTABLE_BASE_ID = 'YOUR_BASE_ID';
const AIRTABLE_TABLE_NAME = 'Table 1'; // The name of your table
const AIRTABLE_ATTACHMENT_FIELD = 'PDF'; // The name of your attachment field

// Supabase Configuration
const SUPABASE_URL = 'https://xyzcompany.supabase.co'; // Your project URL
const SUPABASE_KEY = 'your-supabase-api-key'; // Use the `service_role` key for upload access
const SUPABASE_BUCKET = 'your-bucket-name'; // The name of your storage bucket

// --- Airtable and Supabase Clients ---
const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(AIRTABLE_BASE_ID);

/**
 * Uploads a file buffer to a Supabase Storage bucket.
 * @param {string} filename The name of the file to create in the bucket.
 * @param {Buffer} fileBuffer The file content as a buffer.
 * @param {string} contentType The MIME type of the file.
 * @returns {Promise<boolean>} True if upload was successful, false otherwise.
 */
async function uploadToSupabase(filename, fileBuffer, contentType) {
  try {
    const uploadURL = `${SUPABASE_URL}/storage/v1/object/${SUPABASE_BUCKET}/${filename}`;
    const response = await axios.put(
      uploadURL,
      fileBuffer,
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': contentType,
        },
      }
    );
    return response.status === 200;
  } catch (error) {
    const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error(`Error uploading ${filename} to Supabase:`, errorMessage);
    return false;
  }
}

/**
 * Main function to process all records from Airtable.
 */
async function transferAttachments() {
  console.log('Starting transfer from Airtable to Supabase...');

  base(AIRTABLE_TABLE_NAME).select({
    // You can add filters here, e.g., to only select records from a specific view
    // view: 'Grid view'
  }).eachPage(async (records, fetchNextPage) => {
    console.log(`Found ${records.length} records on this page...`);

    const uploadPromises = records.map(record => {
      const attachments = record.get(AIRTABLE_ATTACHMENT_FIELD);
      if (attachments && attachments.length > 0) {
        // This script processes only the first attachment in the field.
        const attachment = attachments[0];
        const { url, filename, type: contentType } = attachment;

        return (async () => {
          try {
            console.log(`Downloading "${filename}"...`);
            const downloadResponse = await axios.get(url, { responseType: 'arraybuffer' });
            
            console.log(`Uploading "${filename}" to Supabase...`);
            const success = await uploadToSupabase(filename, downloadResponse.data, contentType);
            
            if (success) {
              console.log(`✅ Successfully transferred "${filename}"`);
            } else {
              console.error(`❌ Failed to transfer "${filename}"`);
            }
          } catch (error) {
            console.error(`❌ Error processing "${filename}":`, error.message);
          }
        })();
      }
    });

    // Wait for all uploads on the current page to complete before fetching the next.
    await Promise.all(uploadPromises.filter(p => p));

    fetchNextPage();

  }, (err) => {
    if (err) {
      console.error('An error occurred during the Airtable records processing:', err);
      return;
    }
    console.log('🎉 All pages processed. Transfer complete!');
  });
}

// Run the script
transferAttachments().catch(err => {
    console.error("A critical error stopped the script:", err);
});