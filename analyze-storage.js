const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Analyze storage usage by bucket
 */
async function analyzeStorageUsage() {
  console.log('📊 Analyzing Supabase storage usage...\n');
  
  try {
    // Get all buckets
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
      return;
    }
    
    let totalSize = 0;
    const bucketStats = [];
    
    for (const bucket of buckets) {
      console.log(`🪣 Analyzing bucket: ${bucket.name}`);
      
      // List all files in bucket
      const { data: files, error: filesError } = await supabase.storage
        .from(bucket.name)
        .list('', { limit: 1000, sortBy: { column: 'created_at', order: 'desc' } });
      
      if (filesError) {
        console.error(`❌ Error listing files in ${bucket.name}:`, filesError);
        continue;
      }
      
      let bucketSize = 0;
      let fileCount = 0;
      const fileSizes = [];
      const largestFiles = [];
      
      for (const file of files) {
        if (file.metadata && file.metadata.size) {
          const size = file.metadata.size;
          bucketSize += size;
          fileCount++;
          fileSizes.push(size);
          largestFiles.push({ name: file.name, size: size });
        }
      }
      
      // Sort largest files
      largestFiles.sort((a, b) => b.size - a.size);
      
      // Calculate statistics
      const avgSize = fileCount > 0 ? bucketSize / fileCount : 0;
      const maxSize = fileSizes.length > 0 ? Math.max(...fileSizes) : 0;
      const minSize = fileSizes.length > 0 ? Math.min(...fileSizes) : 0;
      
      bucketStats.push({
        name: bucket.name,
        size: bucketSize,
        fileCount: fileCount,
        avgSize: avgSize,
        maxSize: maxSize,
        minSize: minSize,
        largestFiles: largestFiles.slice(0, 5) // Top 5 largest files
      });
      
      totalSize += bucketSize;
      
      console.log(`   📁 Files: ${fileCount}`);
      console.log(`   📏 Total size: ${formatBytes(bucketSize)}`);
      console.log(`   📊 Average size: ${formatBytes(avgSize)}`);
      console.log(`   📈 Largest file: ${formatBytes(maxSize)}`);
      console.log('');
    }
    
    // Overall statistics
    console.log('🎯 STORAGE SUMMARY');
    console.log('==================');
    console.log(`📊 Total storage used: ${formatBytes(totalSize)}`);
    console.log(`🚨 Free tier limit: ${formatBytes(1024 * 1024 * 1024)} (1GB)`);
    console.log(`📈 Usage: ${((totalSize / (1024 * 1024 * 1024)) * 100).toFixed(1)}%`);
    console.log(`🔄 Remaining: ${formatBytes((1024 * 1024 * 1024) - totalSize)}`);
    console.log('');
    
    // Bucket breakdown
    console.log('📁 BUCKET BREAKDOWN');
    console.log('===================');
    bucketStats.sort((a, b) => b.size - a.size);
    
    for (const bucket of bucketStats) {
      const percentage = totalSize > 0 ? ((bucket.size / totalSize) * 100).toFixed(1) : 0;
      console.log(`🪣 ${bucket.name}:`);
      console.log(`   📏 Size: ${formatBytes(bucket.size)} (${percentage}%)`);
      console.log(`   📁 Files: ${bucket.fileCount}`);
      console.log(`   📊 Avg: ${formatBytes(bucket.avgSize)}`);
      console.log(`   📈 Max: ${formatBytes(bucket.maxSize)}`);
      
      if (bucket.largestFiles.length > 0) {
        console.log(`   🔝 Largest files:`);
        bucket.largestFiles.forEach((file, index) => {
          console.log(`      ${index + 1}. ${file.name} (${formatBytes(file.size)})`);
        });
      }
      console.log('');
    }
    
    // Recommendations
    console.log('💡 OPTIMIZATION RECOMMENDATIONS');
    console.log('===============================');
    
    const pdfBucket = bucketStats.find(b => b.name === 'case-study-pdfs');
    const logoBucket = bucketStats.find(b => b.name === 'company-logos');
    
    if (pdfBucket && pdfBucket.avgSize > 1024 * 1024) { // > 1MB average
      console.log('🔴 PDFs are consuming too much space:');
      console.log(`   • Average PDF size: ${formatBytes(pdfBucket.avgSize)}`);
      console.log(`   • Recommendation: Implement PDF compression or size limits`);
      console.log(`   • Consider storing only URLs for PDFs > 5MB`);
    }
    
    if (logoBucket && logoBucket.avgSize > 100 * 1024) { // > 100KB average
      console.log('🟡 Logos could be compressed further:');
      console.log(`   • Average logo size: ${formatBytes(logoBucket.avgSize)}`);
      console.log(`   • Recommendation: Reduce image dimensions to 200x150px`);
      console.log(`   • Lower JPEG quality to 60%`);
    }
    
    if (totalSize > 800 * 1024 * 1024) { // > 800MB
      console.log('🚨 CRITICAL: Approaching storage limit!');
      console.log('   • Immediate action required');
      console.log('   • Consider implementing URL-only storage for large files');
    }
    
  } catch (error) {
    console.error('💥 Error analyzing storage:', error);
  }
}

/**
 * Check migration progress
 */
async function checkMigrationProgress() {
  console.log('📈 Checking migration progress...\n');
  
  try {
    const { data: records, error } = await supabase
      .from('airtable_data')
      .select('id, airtable_id, name, company, pdf_url, logo_url')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('❌ Error checking migration progress:', error);
      return;
    }
    
    console.log(`📊 Records migrated: ${records.length}`);
    console.log(`📄 Records with PDFs: ${records.filter(r => r.pdf_url).length}`);
    console.log(`🏢 Records with logos: ${records.filter(r => r.logo_url).length}`);
    
    if (records.length > 0) {
      console.log(`🕐 Last migrated: ${records[0].name} (${records[0].company})`);
      console.log(`🆔 Last Airtable ID: ${records[0].airtable_id}`);
    }
    
  } catch (error) {
    console.error('💥 Error checking migration progress:', error);
  }
}

async function main() {
  await analyzeStorageUsage();
  console.log('\n' + '='.repeat(50) + '\n');
  await checkMigrationProgress();
}

main().catch(err => {
  console.error("💥 Critical error:", err);
});
