const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function createBuckets() {
  console.log('🪣 Creating storage buckets...\n');
  
  // Create company-logos bucket
  console.log('📁 Creating company-logos bucket...');
  const { data: logosBucket, error: logosError } = await supabase.storage.createBucket('company-logos', {
    public: true,
    fileSizeLimit: 5242880, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp']
  });
  
  if (logosError && logosError.message !== 'Bucket already exists') {
    console.error('❌ Error creating company-logos bucket:', logosError);
  } else {
    console.log('✅ company-logos bucket ready');
  }
  
  // Create case-study-pdfs bucket
  console.log('📁 Creating case-study-pdfs bucket...');
  const { data: pdfsBucket, error: pdfsError } = await supabase.storage.createBucket('case-study-pdfs', {
    public: true,
    fileSizeLimit: 52428800, // 50MB
    allowedMimeTypes: ['application/pdf']
  });
  
  if (pdfsError && pdfsError.message !== 'Bucket already exists') {
    console.error('❌ Error creating case-study-pdfs bucket:', pdfsError);
  } else {
    console.log('✅ case-study-pdfs bucket ready');
  }
  
  // List all buckets to verify
  console.log('\n📋 Current storage buckets:');
  const { data: buckets, error: listError } = await supabase.storage.listBuckets();
  
  if (listError) {
    console.error('❌ Error listing buckets:', listError);
  } else {
    buckets.forEach(bucket => {
      console.log(`   • ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
    });
  }
  
  console.log('\n🎉 Storage setup complete!');
  console.log('💡 Ready to run: node migrate-simple.js');
}

createBuckets().catch(err => {
  console.error("💥 Error:", err);
});
