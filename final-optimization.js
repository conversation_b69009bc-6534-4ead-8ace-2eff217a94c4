const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Configuration
const SIZE_LIMIT = 2 * 1024 * 1024; // 2MB - files larger than this will use Airtable URLs
const DRY_RUN = true; // Set to false to actually execute changes

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file size from storage
 */
async function getFileSize(filename) {
  try {
    const { data: files } = await supabase.storage
      .from('case-study-pdfs')
      .list('', { search: filename });
    
    if (files && files.length > 0) {
      return files[0].metadata?.size || 0;
    }
    return 0;
  } catch (error) {
    return 0;
  }
}

/**
 * Main optimization function
 */
async function optimizeStorage() {
  console.log('🚀 Final Storage Optimization');
  console.log('============================');
  console.log(`📊 Current usage: 3.07 GB / 1 GB (306.9% over limit)`);
  console.log(`🎯 Target: Under 1 GB (free tier)`);
  console.log(`📏 Strategy: Keep files ≤ ${formatBytes(SIZE_LIMIT)}, use Airtable URLs for larger files`);
  console.log(`⚠️  DRY RUN: ${DRY_RUN ? 'ON (no changes will be made)' : 'OFF (changes will be applied)'}`);
  console.log('');

  let totalProcessed = 0;
  let filesDeleted = 0;
  let recordsUpdated = 0;
  let spaceSaved = 0;
  let errors = 0;

  try {
    // Get all records with PDFs
    const { data: records, error } = await supabase
      .from('airtable_data')
      .select('id, name, company, pdf_url, pdf')
      .not('pdf', 'is', null)
      .not('pdf_url', 'is', null);

    if (error) {
      console.error('❌ Error fetching records:', error);
      return;
    }

    console.log(`📋 Found ${records.length} records with PDFs to analyze`);
    console.log('');

    for (const record of records) {
      totalProcessed++;
      
      if (record.pdf_url && record.pdf_url.includes('case-study-pdfs')) {
        // Extract filename from Supabase URL
        const filename = record.pdf_url.split('/').pop();
        const fileSize = await getFileSize(filename);
        
        if (fileSize > SIZE_LIMIT) {
          console.log(`🔄 Processing: ${record.name} (${record.company})`);
          console.log(`   📏 File size: ${formatBytes(fileSize)}`);
          
          try {
            // Parse original PDF data to get Airtable URL
            const pdfData = JSON.parse(record.pdf);
            if (pdfData && pdfData.length > 0 && pdfData[0].url) {
              const airtableUrl = pdfData[0].url;
              
              console.log(`   🔗 Reverting to Airtable URL`);
              
              if (!DRY_RUN) {
                // Update database record
                const { error: updateError } = await supabase
                  .from('airtable_data')
                  .update({ pdf_url: airtableUrl })
                  .eq('id', record.id);
                
                if (updateError) {
                  console.log(`   ❌ Database update failed: ${updateError.message}`);
                  errors++;
                  continue;
                }
                
                // Delete file from storage
                const { error: deleteError } = await supabase.storage
                  .from('case-study-pdfs')
                  .remove([filename]);
                
                if (deleteError) {
                  console.log(`   ⚠️  File deletion failed: ${deleteError.message}`);
                  // Don't count as error since database was updated
                } else {
                  console.log(`   🗑️  File deleted from storage`);
                  filesDeleted++;
                  spaceSaved += fileSize;
                }
                
                recordsUpdated++;
              } else {
                console.log(`   ✅ Would update database and delete file`);
                spaceSaved += fileSize;
                recordsUpdated++;
                filesDeleted++;
              }
            } else {
              console.log(`   ⚠️  No Airtable URL found in PDF data`);
              errors++;
            }
          } catch (parseError) {
            console.log(`   ❌ Could not parse PDF data: ${parseError.message}`);
            errors++;
          }
          
          console.log('');
        }
      }
      
      // Progress update
      if (totalProcessed % 100 === 0) {
        console.log(`📈 Progress: ${totalProcessed}/${records.length} records processed`);
        console.log('');
      }
    }
    
    // Final results
    console.log('🎉 OPTIMIZATION COMPLETE');
    console.log('========================');
    console.log(`📊 Records processed: ${totalProcessed}`);
    console.log(`🔄 Records updated: ${recordsUpdated}`);
    console.log(`🗑️  Files deleted: ${filesDeleted}`);
    console.log(`💾 Space saved: ${formatBytes(spaceSaved)}`);
    console.log(`❌ Errors: ${errors}`);
    console.log('');
    
    const remainingStorage = (3.07 * 1024 * 1024 * 1024) - spaceSaved;
    const remainingGB = remainingStorage / (1024 * 1024 * 1024);
    
    console.log(`📈 PROJECTED RESULTS:`);
    console.log(`   • Current storage: 3.07 GB`);
    console.log(`   • Space to be freed: ${formatBytes(spaceSaved)}`);
    console.log(`   • Remaining storage: ${remainingGB.toFixed(2)} GB`);
    console.log(`   • Within free tier: ${remainingGB <= 1 ? '✅ YES' : '❌ NO'}`);
    console.log('');
    
    if (DRY_RUN) {
      console.log('⚠️  THIS WAS A DRY RUN - NO CHANGES WERE MADE');
      console.log('💡 To apply these changes, set DRY_RUN = false and run again');
    } else {
      console.log('✅ CHANGES HAVE BEEN APPLIED');
      console.log('🔄 Run the storage analysis script to verify results');
    }
    
    console.log('');
    console.log('📋 SUMMARY:');
    console.log('• Large files (>5MB) now use original Airtable URLs');
    console.log('• Small files (<5MB) remain in optimized Supabase storage');
    console.log('• All data integrity maintained');
    console.log('• Storage usage should now be within free tier limits');
    
  } catch (error) {
    console.error('💥 Critical error:', error);
  }
}

// Run the optimization
console.log('🚀 Starting Final Storage Optimization...\n');
optimizeStorage().catch(err => {
  console.error("💥 Critical error:", err);
});
