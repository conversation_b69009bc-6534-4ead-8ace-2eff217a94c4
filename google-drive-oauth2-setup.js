const { google } = require('googleapis');
const fs = require('fs');
const http = require('http');
const url = require('url');
const { exec } = require('child_process');
require('dotenv').config();

class GoogleDriveOAuth2Setup {
  constructor() {
    this.oauth2Client = null;
    this.credentialsPath = './oauth2-credentials.json';
    this.tokenPath = './google-drive-token.json';
    this.folderId = '1E_IxXxD2_klvg5USwC-ICerHGRTrYfMe'; // Your folder ID
  }

  /**
   * Check if OAuth2 credentials file exists
   */
  checkCredentialsFile() {
    if (!fs.existsSync(this.credentialsPath)) {
      console.log('❌ OAuth2 credentials file not found!');
      console.log('');
      console.log('📋 Please create OAuth2 credentials:');
      console.log('1. Go to: https://console.cloud.google.com/');
      console.log('2. Select project: casestudies-465119');
      console.log('3. Go to: APIs & Services → Credentials');
      console.log('4. Click: Create Credentials → OAuth 2.0 Client IDs');
      console.log('5. Application type: Desktop application');
      console.log('6. Name: PDF Migration Tool');
      console.log('7. Download JSON file');
      console.log('8. Save as: oauth2-credentials.json');
      console.log('9. Place in this folder');
      console.log('');
      return false;
    }
    return true;
  }

  /**
   * Initialize OAuth2 client
   */
  async initialize() {
    try {
      if (!this.checkCredentialsFile()) {
        return false;
      }

      const credentials = JSON.parse(fs.readFileSync(this.credentialsPath, 'utf8'));
      const { client_secret, client_id, redirect_uris } = credentials.installed || credentials.web;

      this.oauth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris?.[0] || 'http://localhost:3000/oauth2callback'
      );

      console.log('✅ OAuth2 client initialized');
      console.log(`   📧 Client ID: ${client_id.substring(0, 20)}...`);
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize OAuth2:', error.message);
      return false;
    }
  }

  /**
   * Check if we have a valid token
   */
  hasValidToken() {
    try {
      if (fs.existsSync(this.tokenPath)) {
        const token = JSON.parse(fs.readFileSync(this.tokenPath, 'utf8'));
        this.oauth2Client.setCredentials(token);
        console.log('✅ Found existing authentication token');
        return true;
      }
      return false;
    } catch (error) {
      console.log('⚠️  Token file exists but is invalid, will re-authenticate');
      return false;
    }
  }

  /**
   * Get authorization URL
   */
  getAuthUrl() {
    const scopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Open URL in browser
   */
  openBrowser(url) {
    const platform = process.platform;
    let command;
    
    if (platform === 'win32') {
      command = `start "${url}"`;
    } else if (platform === 'darwin') {
      command = `open "${url}"`;
    } else {
      command = `xdg-open "${url}"`;
    }
    
    exec(command, (error) => {
      if (error) {
        console.log('⚠️  Could not open browser automatically');
        console.log('📋 Please manually visit this URL:');
        console.log(url);
      }
    });
  }

  /**
   * Start OAuth2 authentication flow
   */
  async authenticate() {
    return new Promise((resolve, reject) => {
      const authUrl = this.getAuthUrl();
      console.log('🔐 Starting Google Drive authentication...');
      console.log('');
      console.log('📱 Opening browser for Google authentication...');
      console.log('   You will be asked to:');
      console.log('   1. Sign in to your Google account');
      console.log('   2. Grant permission to access Google Drive');
      console.log('   3. The browser will redirect back automatically');
      console.log('');

      // Create local server to handle callback
      const server = http.createServer(async (req, res) => {
        const queryObject = url.parse(req.url, true).query;
        
        if (queryObject.code) {
          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(`
            <html>
              <head><title>Google Drive Authentication Successful</title></head>
              <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f8ff;">
                <div style="max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                  <h2 style="color: #28a745; margin-bottom: 20px;">✅ Authentication Successful!</h2>
                  <p style="color: #666; margin-bottom: 20px;">Google Drive access has been granted successfully.</p>
                  <p style="color: #666; margin-bottom: 30px;">You can now close this window and return to the terminal.</p>
                  <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                    <strong style="color: #155724;">Ready to upload PDFs!</strong><br>
                    <span style="color: #155724;">The migration tool can now upload your 800 PDFs to Google Drive</span>
                  </div>
                  <p style="color: #999; font-size: 14px;">Target folder: ${this.folderId}</p>
                </div>
              </body>
            </html>
          `);

          try {
            // Exchange code for tokens
            const { tokens } = await this.oauth2Client.getToken(queryObject.code);
            this.oauth2Client.setCredentials(tokens);

            // Save tokens for future use
            fs.writeFileSync(this.tokenPath, JSON.stringify(tokens, null, 2));
            console.log('✅ Authentication successful!');
            console.log('✅ Access token saved for future use');

            server.close();
            resolve(true);
          } catch (error) {
            console.error('❌ Token exchange failed:', error.message);
            server.close();
            reject(error);
          }
        } else if (queryObject.error) {
          res.writeHead(400, { 'Content-Type': 'text/html' });
          res.end(`
            <html>
              <head><title>Authentication Failed</title></head>
              <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #fff5f5;">
                <div style="max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                  <h2 style="color: #dc3545;">❌ Authentication Failed</h2>
                  <p style="color: #666;">Error: ${queryObject.error}</p>
                  <p style="color: #666;">Please close this window and try again.</p>
                </div>
              </body>
            </html>
          `);
          
          console.error('❌ Authentication failed:', queryObject.error);
          server.close();
          reject(new Error(queryObject.error));
        }
      });

      server.listen(3000, () => {
        console.log('🌐 Local authentication server started on http://localhost:3000');
        console.log('🔗 Opening Google authentication page...');
        console.log('');
        
        // Open browser
        this.openBrowser(authUrl);
        
        console.log('⏳ Waiting for authentication...');
        console.log('   (If browser didn\'t open, copy this URL manually:)');
        console.log(`   ${authUrl}`);
      });

      // Timeout after 10 minutes
      setTimeout(() => {
        server.close();
        reject(new Error('Authentication timeout - please try again'));
      }, 10 * 60 * 1000);
    });
  }

  /**
   * Test Google Drive access
   */
  async testDriveAccess() {
    try {
      const drive = google.drive({ version: 'v3', auth: this.oauth2Client });
      
      console.log('🧪 Testing Google Drive access...');
      
      // Test 1: Get user info
      const aboutResult = await drive.about.get({
        fields: 'user,storageQuota'
      });
      
      console.log('✅ Google Drive API access successful!');
      console.log(`   👤 User: ${aboutResult.data.user.displayName}`);
      console.log(`   📧 Email: ${aboutResult.data.user.emailAddress}`);
      
      // Test 2: Access target folder
      console.log('');
      console.log('🔍 Testing folder access...');
      const folderResult = await drive.files.get({
        fileId: this.folderId,
        fields: 'id,name,parents'
      });
      
      console.log('✅ Target folder access successful!');
      console.log(`   📁 Folder: ${folderResult.data.name}`);
      console.log(`   🆔 ID: ${folderResult.data.id}`);
      console.log(`   🔗 URL: https://drive.google.com/drive/folders/${folderResult.data.id}`);
      
      // Test 3: Create a test file
      console.log('');
      console.log('📤 Testing file upload...');
      const testContent = `Google Drive OAuth2 Test File
Created: ${new Date().toISOString()}
Migration Tool: Ready for PDF uploads
Target Folder: ${this.folderId}`;
      
      const fileName = `oauth2-test-${Date.now()}.txt`;
      
      const fileMetadata = {
        name: fileName,
        parents: [this.folderId]
      };
      
      const media = {
        mimeType: 'text/plain',
        body: require('stream').Readable.from([testContent])
      };
      
      const uploadResult = await drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id,name,webViewLink,webContentLink'
      });
      
      // Make file publicly viewable
      await drive.permissions.create({
        fileId: uploadResult.data.id,
        resource: {
          role: 'reader',
          type: 'anyone'
        }
      });
      
      console.log('✅ File upload test successful!');
      console.log(`   📄 File: ${uploadResult.data.name}`);
      console.log(`   🔗 View: ${uploadResult.data.webViewLink}`);
      console.log(`   📥 Download: ${uploadResult.data.webContentLink}`);
      
      return true;
      
    } catch (error) {
      console.error('❌ Google Drive test failed:', error.message);
      
      if (error.message.includes('insufficient authentication')) {
        console.log('🔧 Solution: Re-run authentication');
        console.log('   Delete google-drive-token.json and run this script again');
      }
      
      return false;
    }
  }

  /**
   * Get authenticated client for migration
   */
  getAuthenticatedClient() {
    return this.oauth2Client;
  }
}

async function main() {
  console.log('🚀 Google Drive OAuth2 Setup for PDF Migration');
  console.log('===============================================');
  console.log('📁 Target folder: https://drive.google.com/drive/folders/1E_IxXxD2_klvg5USwC-ICerHGRTrYfMe');
  console.log('');
  
  const oauth2Setup = new GoogleDriveOAuth2Setup();
  
  // Initialize OAuth2 client
  console.log('🔧 Initializing OAuth2 client...');
  const initialized = await oauth2Setup.initialize();
  if (!initialized) {
    console.log('');
    console.log('❌ Setup failed - please create OAuth2 credentials first');
    return;
  }
  
  // Check for existing token
  if (!oauth2Setup.hasValidToken()) {
    console.log('');
    console.log('🔐 No valid authentication found, starting OAuth2 flow...');
    try {
      await oauth2Setup.authenticate();
    } catch (error) {
      console.error('❌ Authentication failed:', error.message);
      return;
    }
  }
  
  // Test Google Drive access
  console.log('');
  console.log('🧪 Testing Google Drive integration...');
  const testPassed = await oauth2Setup.testDriveAccess();
  
  if (testPassed) {
    console.log('');
    console.log('🎉 SUCCESS! Google Drive is ready for PDF migration!');
    console.log('');
    console.log('✅ Authentication complete');
    console.log('✅ Folder access confirmed');
    console.log('✅ File upload tested');
    console.log('');
    console.log('📋 Next step: Run the PDF migration');
    console.log('   node migrate-pdfs-to-google-drive.js');
    console.log('');
    console.log('📊 This will:');
    console.log('   • Download all 800 PDFs from Airtable');
    console.log('   • Upload them to your Google Drive folder');
    console.log('   • Update database with Google Drive URLs');
    console.log('   • Use proper naming conventions');
    console.log('   • Make files publicly accessible');
  } else {
    console.log('');
    console.log('❌ OAuth2 setup incomplete');
    console.log('🔧 Please check the error messages above and try again');
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error('💥 Setup failed:', err.message);
    process.exit(1);
  });
}

module.exports = GoogleDriveOAuth2Setup;
