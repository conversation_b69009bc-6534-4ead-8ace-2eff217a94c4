const GoogleDriveUploader = require('./google-drive-uploader');
require('dotenv').config();

async function testGoogleDrive() {
  console.log('🧪 Testing Google Drive Integration');
  console.log('===================================');
  
  const uploader = new GoogleDriveUploader();
  
  try {
    // Test initialization
    console.log('1. Testing Google Drive API initialization...');
    const initialized = await uploader.initialize();
    
    if (!initialized) {
      console.error('❌ Failed to initialize Google Drive API');
      console.log('');
      console.log('📋 Setup checklist:');
      console.log('□ Created Google Cloud project');
      console.log('□ Enabled Google Drive API');
      console.log('□ Created service account');
      console.log('□ Downloaded credentials JSON file');
      console.log('□ Created Google Drive folder');
      console.log('□ Shared folder with service account email');
      console.log('□ Set GOOGLE_DRIVE_FOLDER_ID in .env');
      console.log('□ Set GOOGLE_DRIVE_CREDENTIALS_PATH in .env');
      return;
    }
    
    console.log('✅ Google Drive API initialized successfully');
    console.log('');
    
    // Test folder access
    console.log('2. Testing folder access...');
    const folderInfo = await uploader.getFolderInfo();
    console.log(`✅ Folder accessible: ${folderInfo.fileCount} files, ${uploader.formatBytes(folderInfo.totalSize)}`);
    console.log('');
    
    // Test file upload (create a small test file)
    console.log('3. Testing file upload...');
    const testContent = Buffer.from('This is a test PDF content for Google Drive integration test.');
    const testFileName = `test-upload-${Date.now()}.txt`;
    
    const uploadResult = await uploader.uploadFile(testFileName, testContent, 'text/plain');
    console.log('✅ Test file uploaded successfully:');
    console.log(`   📁 Name: ${uploadResult.name}`);
    console.log(`   🔗 View Link: ${uploadResult.viewLink}`);
    console.log(`   📥 Download Link: ${uploadResult.downloadLink}`);
    console.log('');
    
    // Test file existence check
    console.log('4. Testing file existence check...');
    const existsResult = await uploader.fileExists(testFileName);
    console.log(`✅ File existence check: ${existsResult.exists ? 'Found' : 'Not found'}`);
    console.log('');
    
    console.log('🎉 All tests passed! Google Drive integration is ready.');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Run the migration script: node migrate-to-google-drive.js');
    console.log('2. Monitor the migration progress');
    console.log('3. Verify all PDFs are accessible via Google Drive links');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('- Check your Google Drive credentials file');
    console.log('- Verify the folder ID is correct');
    console.log('- Ensure the service account has access to the folder');
    console.log('- Check your internet connection');
  }
}

// Run test if called directly
if (require.main === module) {
  testGoogleDrive().catch(err => {
    console.error('💥 Critical error:', err);
    process.exit(1);
  });
}

module.exports = testGoogleDrive;
