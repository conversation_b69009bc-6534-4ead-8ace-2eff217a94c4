const { createClient } = require('@supabase/supabase-js');

// Supabase Configuration
const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function testTable() {
  console.log('🧪 Testing table existence...');
  
  try {
    // Test if we can select from the table
    const { data, error } = await supabase
      .from('airtable_data')
      .select('id')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') {
        console.log('❌ Table "airtable_data" does not exist');
        console.log('📋 Please create it manually in Supabase dashboard with this SQL:');
        console.log(`
CREATE TABLE airtable_data (
  id SERIAL PRIMARY KEY,
  name TEXT,
  type_field JSONB,
  organizer TEXT,
  company TEXT,
  logo TEXT,
  logo_url TEXT,
  objective JSONB,
  category JSONB,
  image_tags_extra JSONB,
  publish TEXT,
  sort_field NUMERIC,
  likes NUMERIC,
  new_image_tag JSONB,
  likes_filter JSONB,
  likes_filter_formula TEXT,
  creators_tag TEXT,
  market TEXT,
  pdf TEXT,
  pdf_url TEXT,
  airtable_id TEXT UNIQUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);`);
        return false;
      } else {
        console.log('❌ Unexpected error:', error);
        return false;
      }
    } else {
      console.log('✅ Table "airtable_data" exists and is accessible!');
      console.log(`📊 Current record count: ${data ? data.length : 0}`);
      return true;
    }
  } catch (error) {
    console.error('❌ Error testing table:', error.message);
    return false;
  }
}

async function testInsert() {
  console.log('🧪 Testing insert capability...');
  
  try {
    const testData = {
      name: 'Test Record',
      airtable_id: 'test_' + Date.now(),
      company: 'Test Company'
    };
    
    const { data, error } = await supabase
      .from('airtable_data')
      .insert([testData])
      .select();
    
    if (error) {
      console.log('❌ Insert test failed:', error.message);
      return false;
    } else {
      console.log('✅ Insert test successful!');
      console.log('📝 Inserted record:', data[0]);
      
      // Clean up test record
      await supabase
        .from('airtable_data')
        .delete()
        .eq('airtable_id', testData.airtable_id);
      
      console.log('🧹 Test record cleaned up');
      return true;
    }
  } catch (error) {
    console.error('❌ Error testing insert:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Supabase table tests...\n');
  
  const tableExists = await testTable();
  
  if (tableExists) {
    console.log('');
    const insertWorks = await testInsert();
    
    if (insertWorks) {
      console.log('\n🎉 All tests passed! Ready to run migration.');
      console.log('💡 Run: node migrate-schema.js');
    } else {
      console.log('\n⚠️  Table exists but insert failed. Check permissions.');
    }
  } else {
    console.log('\n⚠️  Please create the table first, then run this test again.');
  }
}

runTests().catch(err => {
  console.error("💥 Critical error:", err);
});
