const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Strategy 1: Delete largest files and replace with URLs
 */
async function optimizeByDeletingLargeFiles() {
  console.log('🗑️  Strategy 1: Delete files larger than 10MB and store URLs only\n');
  
  const SIZE_LIMIT = 10 * 1024 * 1024; // 10MB
  let totalSaved = 0;
  let filesDeleted = 0;
  
  try {
    // Get all files from PDF bucket
    const { data: files, error } = await supabase.storage
      .from('case-study-pdfs')
      .list('', { limit: 1000, sortBy: { column: 'created_at', order: 'desc' } });
    
    if (error) {
      console.error('❌ Error listing files:', error);
      return;
    }
    
    console.log(`📊 Found ${files.length} PDF files`);
    
    // Find large files
    const largeFiles = files.filter(file => 
      file.metadata && file.metadata.size > SIZE_LIMIT
    ).sort((a, b) => b.metadata.size - a.metadata.size);
    
    console.log(`🔍 Found ${largeFiles.length} files larger than ${formatBytes(SIZE_LIMIT)}`);
    
    if (largeFiles.length === 0) {
      console.log('✅ No files to delete!');
      return;
    }
    
    console.log('\n📋 Files to be deleted:');
    largeFiles.forEach((file, i) => {
      console.log(`${i + 1}. ${file.name} (${formatBytes(file.metadata.size)})`);
    });
    
    // Calculate savings
    const totalSavings = largeFiles.reduce((sum, file) => sum + file.metadata.size, 0);
    console.log(`\n💰 Total space to be freed: ${formatBytes(totalSavings)}`);
    
    // Delete files (uncomment to actually delete)
    console.log('\n⚠️  To actually delete these files, uncomment the deletion code below');
    
    /*
    for (const file of largeFiles) {
      const { error: deleteError } = await supabase.storage
        .from('case-study-pdfs')
        .remove([file.name]);
      
      if (deleteError) {
        console.error(`❌ Error deleting ${file.name}:`, deleteError);
      } else {
        console.log(`✅ Deleted ${file.name} (${formatBytes(file.metadata.size)})`);
        totalSaved += file.metadata.size;
        filesDeleted++;
      }
    }
    */
    
    console.log(`\n📊 Potential Results:`);
    console.log(`   • Files that would be deleted: ${largeFiles.length}`);
    console.log(`   • Space that would be freed: ${formatBytes(totalSavings)}`);
    console.log(`   • Remaining storage: ${formatBytes(3.07 * 1024 * 1024 * 1024 - totalSavings)}`);
    
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

/**
 * Strategy 2: Keep only essential files
 */
async function optimizeByKeepingEssentials() {
  console.log('🎯 Strategy 2: Keep only files smaller than 5MB\n');
  
  const SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
  
  try {
    const { data: files, error } = await supabase.storage
      .from('case-study-pdfs')
      .list('', { limit: 1000 });
    
    if (error) {
      console.error('❌ Error listing files:', error);
      return;
    }
    
    const smallFiles = files.filter(file => 
      file.metadata && file.metadata.size <= SIZE_LIMIT
    );
    
    const largeFiles = files.filter(file => 
      file.metadata && file.metadata.size > SIZE_LIMIT
    );
    
    const smallFilesSize = smallFiles.reduce((sum, file) => sum + file.metadata.size, 0);
    const largeFilesSize = largeFiles.reduce((sum, file) => sum + file.metadata.size, 0);
    
    console.log(`📊 Analysis:`);
    console.log(`   • Files ≤ ${formatBytes(SIZE_LIMIT)}: ${smallFiles.length} files (${formatBytes(smallFilesSize)})`);
    console.log(`   • Files > ${formatBytes(SIZE_LIMIT)}: ${largeFiles.length} files (${formatBytes(largeFilesSize)})`);
    console.log(`   • Total if keeping only small files: ${formatBytes(smallFilesSize + 8.75 * 1024 * 1024)}`);
    console.log(`   • Would fit in free tier: ${smallFilesSize + 8.75 * 1024 * 1024 < 1024 * 1024 * 1024 ? '✅ YES' : '❌ NO'}`);
    
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

/**
 * Strategy 3: Update database to use original URLs for large files
 */
async function updateDatabaseForLargeFiles() {
  console.log('🔄 Strategy 3: Update database to use original Airtable URLs for large files\n');
  
  try {
    // Get records with large PDFs
    const { data: records, error } = await supabase
      .from('airtable_data')
      .select('id, name, company, pdf_url, pdf')
      .not('pdf', 'is', null);
    
    if (error) {
      console.error('❌ Error fetching records:', error);
      return;
    }
    
    console.log(`📊 Found ${records.length} records with PDFs`);
    
    let largeFileRecords = 0;
    
    for (const record of records) {
      if (record.pdf_url && record.pdf_url.includes('case-study-pdfs')) {
        // Extract filename from URL
        const filename = record.pdf_url.split('/').pop();
        
        // Check file size
        const { data: fileInfo } = await supabase.storage
          .from('case-study-pdfs')
          .list('', { search: filename });
        
        if (fileInfo && fileInfo.length > 0) {
          const file = fileInfo[0];
          if (file.metadata && file.metadata.size > 5 * 1024 * 1024) {
            largeFileRecords++;
            
            // Parse original PDF data to get Airtable URL
            try {
              const pdfData = JSON.parse(record.pdf);
              if (pdfData && pdfData.length > 0 && pdfData[0].url) {
                console.log(`🔄 Would update ${record.name}: ${formatBytes(file.metadata.size)} → Airtable URL`);
                
                // Uncomment to actually update
                /*
                const { error: updateError } = await supabase
                  .from('airtable_data')
                  .update({ pdf_url: pdfData[0].url })
                  .eq('id', record.id);
                
                if (updateError) {
                  console.error(`❌ Error updating ${record.name}:`, updateError);
                }
                */
              }
            } catch (parseError) {
              console.log(`⚠️  Could not parse PDF data for ${record.name}`);
            }
          }
        }
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`   • Records with large PDFs: ${largeFileRecords}`);
    console.log(`   • These could be reverted to Airtable URLs`);
    
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

/**
 * Main optimization function
 */
async function main() {
  console.log('🚀 Supabase Storage Optimization Tool\n');
  console.log('Current usage: 3.07 GB / 1 GB (306.9% over limit)\n');
  
  await optimizeByDeletingLargeFiles();
  console.log('\n' + '='.repeat(60) + '\n');
  
  await optimizeByKeepingEssentials();
  console.log('\n' + '='.repeat(60) + '\n');
  
  await updateDatabaseForLargeFiles();
  
  console.log('\n💡 Recommendations:');
  console.log('1. Delete files > 10MB and use Airtable URLs (saves ~2GB)');
  console.log('2. Keep only files < 5MB in Supabase storage');
  console.log('3. Update database records to point to Airtable for large files');
  console.log('4. Consider upgrading to Supabase Pro ($25/month) for 8GB storage');
}

main().catch(err => {
  console.error("💥 Critical error:", err);
});
