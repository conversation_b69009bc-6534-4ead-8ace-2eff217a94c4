const FreshAirtableImporter = require('./import-fresh-airtable-data');
const PDFToGoogleDriveServiceAccount = require('./migrate-pdfs-service-account');
const fs = require('fs');

class CompleteMigrationPipeline {
  constructor() {
    this.importer = new FreshAirtableImporter();
    this.migrator = new PDFToGoogleDriveServiceAccount();
  }

  /**
   * Execute complete migration pipeline
   */
  async execute() {
    console.log('🚀 COMPLETE PDF MIGRATION PIPELINE');
    console.log('==================================');
    console.log('📋 Steps:');
    console.log('   1. Import fresh Airtable data');
    console.log('   2. Migrate PDFs to Google Drive');
    console.log('   3. Update database with Google Drive URLs');
    console.log('');

    // Step 1: Import fresh data
    console.log('📥 STEP 1: Importing Fresh Airtable Data');
    console.log('========================================');
    
    const importSuccess = await this.importFreshData();
    if (!importSuccess) {
      console.log('❌ Import failed - cannot proceed with migration');
      return false;
    }

    // Wait a moment for database to sync
    console.log('⏳ Waiting 5 seconds for database sync...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 2: Migrate to Google Drive
    console.log('');
    console.log('☁️  STEP 2: Migrating PDFs to Google Drive');
    console.log('==========================================');
    
    const migrationSuccess = await this.migrator.migrate();
    
    if (migrationSuccess !== false) {
      console.log('');
      console.log('🎉 COMPLETE MIGRATION SUCCESSFUL!');
      console.log('=================================');
      console.log('✅ Fresh Airtable data imported');
      console.log('✅ PDFs migrated to Google Drive');
      console.log('✅ Database updated with Google Drive URLs');
      console.log('✅ Production-ready solution deployed');
      console.log('');
      console.log(`📁 View all files: https://drive.google.com/drive/folders/${this.migrator.folderId}`);
      return true;
    } else {
      console.log('❌ Migration failed - check logs above');
      return false;
    }
  }

  /**
   * Import fresh Airtable data
   */
  async importFreshData() {
    // Check for export files
    const csvFiles = ['airtable-export.csv', 'fresh-data.csv', 'case-studies.csv'];
    const jsonFiles = ['airtable-export.json', 'fresh-data.json', 'case-studies.json'];
    
    let importFile = null;
    let importType = null;
    
    // Look for CSV files first
    for (const file of csvFiles) {
      if (fs.existsSync(file)) {
        importFile = file;
        importType = 'csv';
        break;
      }
    }
    
    // If no CSV, look for JSON
    if (!importFile) {
      for (const file of jsonFiles) {
        if (fs.existsSync(file)) {
          importFile = file;
          importType = 'json';
          break;
        }
      }
    }
    
    if (!importFile) {
      console.log('❌ No Airtable export file found!');
      console.log('');
      console.log('📋 Please export fresh data from Airtable:');
      console.log('');
      console.log('🔗 Airtable Export Steps:');
      console.log('1. Open your Airtable base');
      console.log('2. Go to the table with case studies');
      console.log('3. Click "..." menu → "Download CSV" or "Download JSON"');
      console.log('4. Save as one of these names:');
      console.log('   • airtable-export.csv');
      console.log('   • fresh-data.csv');
      console.log('   • case-studies.csv');
      console.log('   • airtable-export.json');
      console.log('   • fresh-data.json');
      console.log('   • case-studies.json');
      console.log('5. Place the file in this directory');
      console.log('6. Run this script again');
      console.log('');
      console.log('⚠️  Important: Export must include Name, Company, and PDF fields');
      return false;
    }
    
    console.log(`📁 Found export file: ${importFile} (${importType.toUpperCase()})`);
    console.log('');
    
    let success = false;
    if (importType === 'csv') {
      success = await this.importer.importFromCSV(importFile);
    } else {
      success = await this.importer.importFromJSON(importFile);
    }
    
    if (success && this.importer.stats.freshUrls > 0) {
      console.log('✅ Fresh data import successful!');
      console.log(`🔗 Found ${this.importer.stats.freshUrls} fresh URLs ready for migration`);
      return true;
    } else {
      console.log('❌ Import failed or no fresh URLs found');
      return false;
    }
  }
}

async function main() {
  const pipeline = new CompleteMigrationPipeline();
  
  console.log('🎯 Production PDF Migration Pipeline');
  console.log('====================================');
  console.log('');
  console.log('This will:');
  console.log('✅ Import fresh Airtable data with non-expired URLs');
  console.log('✅ Migrate all PDFs to Google Drive');
  console.log('✅ Update database with Google Drive URLs');
  console.log('✅ Provide unlimited storage with no rate limits');
  console.log('');
  
  // Check if Google Drive is ready
  console.log('🔧 Checking Google Drive setup...');
  const authReady = await pipeline.migrator.initializeAuth();
  if (!authReady) {
    console.log('❌ Google Drive authentication not ready');
    console.log('📋 Please ensure oauth2-credentials.json is properly configured');
    return;
  }
  
  const testPassed = await pipeline.migrator.testDriveAccess();
  if (!testPassed) {
    console.log('❌ Google Drive access test failed');
    return;
  }
  
  console.log('✅ Google Drive ready for migration');
  console.log('');
  
  // Execute complete pipeline
  const success = await pipeline.execute();
  
  if (success) {
    console.log('');
    console.log('🎊 MIGRATION COMPLETE - PRODUCTION READY!');
    console.log('=========================================');
    console.log('');
    console.log('🔗 Your application now has:');
    console.log('   • Unlimited PDF storage (Google Drive)');
    console.log('   • No API rate limits');
    console.log('   • Publicly accessible URLs');
    console.log('   • Proper file naming conventions');
    console.log('   • Production-grade reliability');
    console.log('');
    console.log('🚀 Ready for production deployment!');
  } else {
    console.log('');
    console.log('❌ Migration failed - check logs above for details');
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error('💥 Pipeline failed:', err.message);
    process.exit(1);
  });
}

module.exports = CompleteMigrationPipeline;
