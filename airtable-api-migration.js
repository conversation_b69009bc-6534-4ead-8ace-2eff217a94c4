const { createClient } = require('@supabase/supabase-js');
const { google } = require('googleapis');
const fs = require('fs');
const https = require('https');
require('dotenv').config();

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

class AirtableToGoogleDriveMigration {
  constructor() {
    this.auth = null;
    this.drive = null;
    this.folderId = '1E_IxXxD2_klvg5USwC-ICerHGRTrYfMe';
    this.airtableToken = process.env.AIRTABLE_TOKEN;
    this.airtableBaseId = process.env.AIRTABLE_BASE_ID;
    this.airtableTableName = process.env.AIRTABLE_TABLE_NAME || 'Case Studies';
    this.stats = {
      totalRecords: 0,
      processed: 0,
      uploaded: 0,
      skipped: 0,
      errors: 0,
      totalSize: 0
    };
  }

  /**
   * Initialize Google Drive authentication
   */
  async initializeAuth() {
    try {
      if (!fs.existsSync('./oauth2-credentials.json')) {
        console.log('❌ Service account credentials not found!');
        return false;
      }

      const credentials = JSON.parse(fs.readFileSync('./oauth2-credentials.json', 'utf8'));
      
      if (credentials.type !== 'service_account') {
        console.log('❌ Expected service account credentials');
        return false;
      }

      this.auth = new google.auth.GoogleAuth({
        credentials: credentials,
        scopes: [
          'https://www.googleapis.com/auth/drive',
          'https://www.googleapis.com/auth/drive.file'
        ]
      });

      this.drive = google.drive({ version: 'v3', auth: this.auth });

      console.log('✅ Google Drive authentication initialized');
      console.log(`   📧 Service Account: ${credentials.client_email}`);
      return true;
    } catch (error) {
      console.error('❌ Auth initialization failed:', error.message);
      return false;
    }
  }

  /**
   * Fetch fresh data from Airtable API
   */
  async fetchFromAirtable() {
    if (!this.airtableToken || !this.airtableBaseId) {
      console.log('❌ Airtable credentials missing!');
      console.log('');
      console.log('📋 Please add to your .env file:');
      console.log('AIRTABLE_TOKEN=your_airtable_token');
      console.log('AIRTABLE_BASE_ID=your_base_id');
      console.log('AIRTABLE_TABLE_NAME=Case Studies');
      console.log('');
      console.log('🔗 Get your token: https://airtable.com/create/tokens');
      console.log('🔗 Find your base ID in the URL: https://airtable.com/appXXXXXXXXXXXXXX/...');
      return [];
    }

    console.log('📡 Fetching fresh data from Airtable API...');
    console.log(`   🔑 Token: ${this.airtableToken.substring(0, 10)}...`);
    console.log(`   📊 Base: ${this.airtableBaseId}`);
    console.log(`   📋 Table: ${this.airtableTableName}`);
    console.log('');

    const records = [];
    let offset = null;

    try {
      do {
        const url = `https://api.airtable.com/v0/${this.airtableBaseId}/${encodeURIComponent(this.airtableTableName)}?pageSize=100${offset ? `&offset=${offset}` : ''}`;
        
        const response = await this.makeAirtableRequest(url);
        const data = JSON.parse(response);

        if (data.error) {
          throw new Error(`Airtable API error: ${data.error.message}`);
        }

        records.push(...data.records);
        offset = data.offset;

        console.log(`📥 Fetched ${records.length} records so far...`);

      } while (offset);

      console.log(`✅ Successfully fetched ${records.length} records from Airtable`);
      return records;

    } catch (error) {
      console.error('❌ Airtable fetch failed:', error.message);
      
      if (error.message.includes('401')) {
        console.log('🔧 Solution: Check your AIRTABLE_TOKEN');
      } else if (error.message.includes('404')) {
        console.log('🔧 Solution: Check your AIRTABLE_BASE_ID and AIRTABLE_TABLE_NAME');
      }
      
      return [];
    }
  }

  /**
   * Make HTTP request to Airtable API
   */
  async makeAirtableRequest(url) {
    return new Promise((resolve, reject) => {
      const options = {
        headers: {
          'Authorization': `Bearer ${this.airtableToken}`,
          'User-Agent': 'PDF Migration Tool'
        }
      };

      const req = https.get(url, options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(30000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Download file from URL
   */
  async downloadFile(url) {
    return new Promise((resolve, reject) => {
      const timeout = 30000;
      
      const request = https.get(url, { timeout }, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        const chunks = [];
        let totalSize = 0;
        
        response.on('data', (chunk) => {
          chunks.push(chunk);
          totalSize += chunk.length;
          
          if (totalSize > 100 * 1024 * 1024) {
            response.destroy();
            reject(new Error('File too large (>100MB)'));
            return;
          }
        });
        
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      });
      
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Download timeout'));
      });
      
      request.on('error', reject);
    });
  }

  /**
   * Upload file to Google Drive
   */
  async uploadToGoogleDrive(fileName, fileBuffer) {
    try {
      const fileMetadata = {
        name: fileName,
        parents: [this.folderId]
      };

      const media = {
        mimeType: 'application/pdf',
        body: require('stream').Readable.from(fileBuffer)
      };

      console.log(`   📤 Uploading ${this.formatBytes(fileBuffer.length)}...`);
      
      const response = await this.drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id,name,webViewLink,webContentLink'
      });

      // Make file publicly viewable
      await this.drive.permissions.create({
        fileId: response.data.id,
        resource: {
          role: 'reader',
          type: 'anyone'
        }
      });

      return {
        id: response.data.id,
        name: response.data.name,
        viewLink: response.data.webViewLink,
        downloadLink: response.data.webContentLink,
        size: fileBuffer.length
      };
    } catch (error) {
      console.error(`   ❌ Upload failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate clean filename
   */
  generateFileName(company, originalName, timestamp) {
    const cleanCompany = (company || 'unknown')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 30);

    const cleanOriginal = (originalName || 'document')
      .replace(/\.[^/.]+$/, '')
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50);

    return `${cleanCompany}-${cleanOriginal}-${timestamp}.pdf`;
  }

  /**
   * Format bytes
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Calculate estimated time remaining
   */
  calculateETA() {
    if (this.stats.processed === 0) return '∞';
    const avgTimePerRecord = (Date.now() - this.startTime) / this.stats.processed;
    const remainingRecords = this.stats.totalRecords - this.stats.processed;
    const etaMs = remainingRecords * avgTimePerRecord;
    return Math.ceil(etaMs / 60000);
  }

  /**
   * Retry mechanism for failed operations
   */
  async retryOperation(operation, maxRetries = 3, delay = 2000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        console.log(`   ⏳ Retry ${attempt}/${maxRetries} in ${delay/1000}s: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2;
      }
    }
  }

  /**
   * Execute complete migration
   */
  async migrate() {
    console.log('🚀 Airtable API to Google Drive Migration');
    console.log('=========================================');
    console.log(`📁 Target folder: https://drive.google.com/drive/folders/${this.folderId}`);
    console.log('🎯 Using fresh Airtable API data (no expired URLs!)');
    console.log('');

    // Initialize Google Drive
    const authReady = await this.initializeAuth();
    if (!authReady) {
      return;
    }

    // Fetch fresh data from Airtable
    const airtableRecords = await this.fetchFromAirtable();
    if (airtableRecords.length === 0) {
      console.log('❌ No records fetched from Airtable');
      return;
    }

    this.stats.totalRecords = airtableRecords.length;
    this.startTime = Date.now();

    console.log('');
    console.log('🔄 Starting migration with fresh URLs...');
    console.log(`⏱️  Estimated time: ${Math.ceil(airtableRecords.length * 3 / 60)} minutes`);
    console.log('');

    // Process records
    for (let i = 0; i < airtableRecords.length; i++) {
      const record = airtableRecords[i];
      await this.migrateRecord(record, i + 1);

      // Progress update every 10 records
      if (this.stats.processed % 10 === 0) {
        console.log('');
        console.log(`📈 Progress: ${this.stats.processed}/${this.stats.totalRecords} (${Math.round(this.stats.processed/this.stats.totalRecords*100)}%)`);
        console.log(`   ✅ Uploaded: ${this.stats.uploaded}`);
        console.log(`   ⏭️  Skipped: ${this.stats.skipped}`);
        console.log(`   ❌ Errors: ${this.stats.errors}`);
        console.log(`   💾 Total size: ${this.formatBytes(this.stats.totalSize)}`);
        console.log(`   ⏱️  ETA: ${this.calculateETA()} minutes`);
        console.log('');
      }

      // Adaptive delay
      const successRate = this.stats.uploaded / (this.stats.uploaded + this.stats.errors);
      const delay = successRate > 0.9 ? 300 : 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Final results
    console.log('');
    console.log('🎉 MIGRATION COMPLETE!');
    console.log('======================');
    console.log(`📊 Total records: ${this.stats.totalRecords}`);
    console.log(`✅ Successfully uploaded: ${this.stats.uploaded}`);
    console.log(`⏭️  Skipped: ${this.stats.skipped}`);
    console.log(`❌ Errors: ${this.stats.errors}`);
    console.log(`💾 Total uploaded: ${this.formatBytes(this.stats.totalSize)}`);
    console.log('');
    console.log('🔗 All PDFs are now:');
    console.log('   • Stored in your Google Drive folder');
    console.log('   • Publicly accessible via Google Drive URLs');
    console.log('   • Updated in your Supabase database');
    console.log('   • Named with proper conventions');
    console.log('');
    console.log(`📁 View all files: https://drive.google.com/drive/folders/${this.folderId}`);
  }

  /**
   * Migrate single record
   */
  async migrateRecord(airtableRecord, index) {
    try {
      this.stats.processed++;
      
      const fields = airtableRecord.fields;
      const name = fields['Name'] || fields['Case Study Name'] || fields['Title'] || 'Unknown';
      const company = fields['Company'] || fields['Organization'] || 'Unknown';
      const pdfField = fields['PDF'] || fields['Attachment'] || fields['File'];

      console.log(`📄 ${index}/${this.stats.totalRecords}: ${name} (${company})`);

      if (!pdfField || !Array.isArray(pdfField) || pdfField.length === 0) {
        console.log(`   ⏭️  No PDF attachment found`);
        this.stats.skipped++;
        return;
      }

      const pdfAttachment = pdfField[0];
      const freshUrl = pdfAttachment.url;
      const originalFilename = pdfAttachment.filename || 'document.pdf';

      console.log(`   📥 Downloading from fresh Airtable URL...`);
      const fileBuffer = await this.retryOperation(() => this.downloadFile(freshUrl));

      const timestamp = Date.now();
      const driveFilename = this.generateFileName(company, originalFilename, timestamp);

      console.log(`   📤 Uploading to Google Drive: ${driveFilename}`);
      const uploadResult = await this.retryOperation(() => this.uploadToGoogleDrive(driveFilename, fileBuffer));

      // Update database
      console.log(`   💾 Updating database...`);
      const { data: existingRecords, error: searchError } = await supabase
        .from('airtable_data')
        .select('id')
        .ilike('name', `%${name}%`)
        .limit(1);

      if (searchError) {
        throw new Error(`Database search failed: ${searchError.message}`);
      }

      if (existingRecords && existingRecords.length > 0) {
        const { error: updateError } = await supabase
          .from('airtable_data')
          .update({
            pdf_url: uploadResult.viewLink,
            pdf: JSON.stringify([{
              url: uploadResult.viewLink,
              filename: driveFilename,
              type: 'application/pdf'
            }])
          })
          .eq('id', existingRecords[0].id);

        if (updateError) {
          throw new Error(`Database update failed: ${updateError.message}`);
        }
      }

      this.stats.uploaded++;
      this.stats.totalSize += uploadResult.size;

      console.log(`   ✅ Success: ${uploadResult.viewLink}`);

    } catch (error) {
      this.stats.errors++;
      console.error(`   ❌ Failed: ${error.message}`);
    }
  }
}

// Run migration
if (require.main === module) {
  const migration = new AirtableToGoogleDriveMigration();
  migration.migrate().catch(err => {
    console.error('💥 Critical error:', err.message);
    process.exit(1);
  });
}

module.exports = AirtableToGoogleDriveMigration;
