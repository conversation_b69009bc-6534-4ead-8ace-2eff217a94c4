const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function quickCheck() {
  console.log('🔍 Quick Migration Status Check\n');
  
  try {
    // Check migration progress
    const { data: records, error } = await supabase
      .from('airtable_data')
      .select('id, name, company, pdf_url, logo_url, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log(`📊 Total records migrated: ${records.length > 0 ? 'Checking...' : 0}`);
    
    // Get total count
    const { count, error: countError } = await supabase
      .from('airtable_data')
      .select('*', { count: 'exact', head: true });
    
    if (!countError) {
      console.log(`📈 Total records: ${count}`);
    }
    
    // Count files
    const { data: withPdfs } = await supabase
      .from('airtable_data')
      .select('pdf_url', { count: 'exact', head: true })
      .not('pdf_url', 'is', null);
    
    const { data: withLogos } = await supabase
      .from('airtable_data')
      .select('logo_url', { count: 'exact', head: true })
      .not('logo_url', 'is', null);
    
    console.log(`📄 Records with PDFs: ${withPdfs?.length || 'Unknown'}`);
    console.log(`🏢 Records with logos: ${withLogos?.length || 'Unknown'}`);
    
    if (records.length > 0) {
      console.log('\n📋 Latest 5 records:');
      records.slice(0, 5).forEach((record, i) => {
        console.log(`${i + 1}. ${record.name} (${record.company})`);
        console.log(`   PDF: ${record.pdf_url ? '✅' : '❌'}`);
        console.log(`   Logo: ${record.logo_url ? '✅' : '❌'}`);
      });
    }
    
    // Check storage buckets
    console.log('\n🪣 Storage Buckets:');
    const { data: buckets } = await supabase.storage.listBuckets();
    
    if (buckets) {
      for (const bucket of buckets) {
        const { data: files } = await supabase.storage
          .from(bucket.name)
          .list('', { limit: 1000 });
        
        console.log(`   • ${bucket.name}: ${files?.length || 0} files`);
      }
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

quickCheck();
