const Airtable = require("airtable");
const axios = require("axios");
const { createClient } = require('@supabase/supabase-js');

// --- Configuration ---
// Airtable Configuration
const AIRTABLE_API_KEY = '**********************************************************************************';
const AIRTABLE_BASE_ID = 'appYfr5h5YVzQO8GZ';
const AIRTABLE_TABLE_NAME = 'Data';

// Supabase Configuration
const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';
const SUPABASE_BUCKET = 'stare-casestudies';
const SUPABASE_TABLE_NAME = 'airtable'; // The table name you mentioned

// --- Clients ---
const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(AIRTABLE_BASE_ID);
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Get Airtable table schema by analyzing the first few records
 */
async function getAirtableSchema() {
  console.log('🔍 Analyzing Airtable schema...');
  
  return new Promise((resolve, reject) => {
    const fieldTypes = {};
    const sampleData = [];
    
    base(AIRTABLE_TABLE_NAME).select({
      maxRecords: 10, // Analyze first 10 records to understand schema
      view: 'Grid view'
    }).eachPage((records, fetchNextPage) => {
      records.forEach(record => {
        const fields = record.fields;
        sampleData.push(fields);
        
        // Analyze field types
        Object.keys(fields).forEach(fieldName => {
          const value = fields[fieldName];
          
          if (!fieldTypes[fieldName]) {
            fieldTypes[fieldName] = {
              name: fieldName,
              type: getFieldType(value),
              samples: []
            };
          }
          
          if (fieldTypes[fieldName].samples.length < 3) {
            fieldTypes[fieldName].samples.push(value);
          }
        });
      });
      
      fetchNextPage();
    }, (err) => {
      if (err) {
        reject(err);
        return;
      }
      
      console.log('📋 Found fields:', Object.keys(fieldTypes));
      resolve({ fieldTypes, sampleData });
    });
  });
}

/**
 * Determine field type based on value
 */
function getFieldType(value) {
  if (value === null || value === undefined) return 'text';
  if (typeof value === 'string') return 'text';
  if (typeof value === 'number') return 'numeric';
  if (typeof value === 'boolean') return 'boolean';
  if (Array.isArray(value)) {
    // Check if it's an attachment field
    if (value.length > 0 && value[0].url && value[0].filename) {
      return 'attachment';
    }
    return 'json'; // For other arrays
  }
  if (typeof value === 'object') return 'json';
  return 'text';
}

/**
 * Convert Airtable field type to PostgreSQL type
 */
function airtableToPostgresType(airtableType) {
  switch (airtableType) {
    case 'text': return 'TEXT';
    case 'numeric': return 'NUMERIC';
    case 'boolean': return 'BOOLEAN';
    case 'attachment': return 'TEXT'; // Store as URL
    case 'json': return 'JSONB';
    default: return 'TEXT';
  }
}

/**
 * Create Supabase table with matching schema
 */
async function createSupabaseTable(fieldTypes) {
  console.log('🏗️  Creating Supabase table...');

  // Build CREATE TABLE SQL
  const columns = ['id SERIAL PRIMARY KEY'];
  const fieldColumns = [];

  Object.values(fieldTypes).forEach(field => {
    const pgType = airtableToPostgresType(field.type);
    const columnName = field.name.toLowerCase().replace(/[^a-z0-9_]/g, '_');
    fieldColumns.push(`"${columnName}" ${pgType}`);

    // For attachment fields, also create a URL column
    if (field.type === 'attachment') {
      fieldColumns.push(`"${columnName}_url" TEXT`);
    }
  });

  columns.push(...fieldColumns);
  columns.push('"airtable_id" TEXT UNIQUE'); // Store original Airtable record ID
  columns.push('"created_at" TIMESTAMP DEFAULT NOW()');
  columns.push('"updated_at" TIMESTAMP DEFAULT NOW()');

  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS "${SUPABASE_TABLE_NAME}" (
      ${columns.join(',\n      ')}
    );
  `;

  console.log('📝 SQL to create table:');
  console.log(createTableSQL);

  try {
    // Use Supabase RPC to execute SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: createTableSQL
    });

    if (error) {
      console.error('❌ Error creating table:', error);
      return false;
    }

    console.log('✅ Table created successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error creating table:', error.message);

    // Fallback: Try to create table manually by checking if it exists
    console.log('🔄 Trying alternative approach...');

    // Check if table exists by trying to select from it
    const { data: existingData, error: selectError } = await supabase
      .from(SUPABASE_TABLE_NAME)
      .select('id')
      .limit(1);

    if (!selectError) {
      console.log('✅ Table already exists!');
      return true;
    }

    console.log('ℹ️  Table doesn\'t exist. You may need to create it manually in Supabase dashboard.');
    console.log('📋 Use this SQL in the Supabase SQL editor:');
    console.log(createTableSQL);
    return false;
  }
}

/**
 * Upload file to Supabase storage and return URL
 */
async function uploadToSupabase(filename, fileBuffer, contentType) {
  try {
    const uploadURL = `${SUPABASE_URL}/storage/v1/object/${SUPABASE_BUCKET}/${filename}`;
    const response = await axios.put(
      uploadURL,
      fileBuffer,
      {
        headers: {
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': contentType,
        },
      }
    );
    
    if (response.status === 200) {
      // Return the public URL
      return `${SUPABASE_URL}/storage/v1/object/public/${SUPABASE_BUCKET}/${filename}`;
    }
    return null;
  } catch (error) {
    console.error(`Error uploading ${filename}:`, error.response?.data || error.message);
    return null;
  }
}

/**
 * Process attachment field and return URL
 */
async function processAttachment(attachments) {
  if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
    return null;
  }
  
  const attachment = attachments[0]; // Process first attachment
  const { url, filename, type: contentType } = attachment;
  
  try {
    console.log(`📥 Downloading "${filename}"...`);
    const downloadResponse = await axios.get(url, { responseType: 'arraybuffer' });
    
    console.log(`📤 Uploading "${filename}" to Supabase...`);
    const supabaseUrl = await uploadToSupabase(filename, downloadResponse.data, contentType);
    
    if (supabaseUrl) {
      console.log(`✅ Successfully uploaded "${filename}"`);
      return supabaseUrl;
    } else {
      console.log(`❌ Failed to upload "${filename}"`);
      return url; // Fallback to original Airtable URL
    }
  } catch (error) {
    console.error(`❌ Error processing "${filename}":`, error.message);
    return url; // Fallback to original Airtable URL
  }
}

/**
 * Insert record into Supabase
 */
async function insertRecord(record, fieldTypes) {
  const data = {
    airtable_id: record.id
  };

  // Process each field
  for (const [fieldName, value] of Object.entries(record.fields)) {
    const fieldType = fieldTypes[fieldName];
    const columnName = fieldName.toLowerCase().replace(/[^a-z0-9_]/g, '_');

    if (fieldType?.type === 'attachment') {
      // Process attachment and get URL
      const attachmentUrl = await processAttachment(value);
      data[columnName] = JSON.stringify(value); // Store original attachment data
      data[`${columnName}_url`] = attachmentUrl; // Store processed URL
    } else if (typeof value === 'object') {
      data[columnName] = JSON.stringify(value);
    } else {
      data[columnName] = value;
    }
  }

  try {
    const { data: insertedData, error } = await supabase
      .from(SUPABASE_TABLE_NAME)
      .insert([data]);

    if (error) {
      console.error('❌ Error inserting record:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Error inserting record:', error.message);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateData() {
  console.log('🚀 Starting Airtable to Supabase migration...');
  
  try {
    // Step 1: Analyze Airtable schema
    const { fieldTypes } = await getAirtableSchema();
    
    // Step 2: Create Supabase table (skip for now - create manually)
    console.log('⏭️  Skipping table creation - please create the table manually in Supabase dashboard');
    console.log('📋 Use this SQL in Supabase SQL Editor:');

    const columns = ['id SERIAL PRIMARY KEY'];
    const fieldColumns = [];

    Object.values(fieldTypes).forEach(field => {
      const pgType = airtableToPostgresType(field.type);
      const columnName = field.name.toLowerCase().replace(/[^a-z0-9_]/g, '_');
      fieldColumns.push(`"${columnName}" ${pgType}`);

      // For attachment fields, also create a URL column
      if (field.type === 'attachment') {
        fieldColumns.push(`"${columnName}_url" TEXT`);
      }
    });

    columns.push(...fieldColumns);
    columns.push('"airtable_id" TEXT UNIQUE');
    columns.push('"created_at" TIMESTAMP DEFAULT NOW()');
    columns.push('"updated_at" TIMESTAMP DEFAULT NOW()');

    const createTableSQL = `
CREATE TABLE IF NOT EXISTS "${SUPABASE_TABLE_NAME}" (
  ${columns.join(',\n  ')}
);`;

    console.log(createTableSQL);
    console.log('\n🔄 Continuing with data migration...');
    
    // Step 3: Migrate data
    console.log('📊 Starting data migration...');
    let totalRecords = 0;
    let successfulRecords = 0;
    
    await new Promise((resolve, reject) => {
      base(AIRTABLE_TABLE_NAME).select({
        view: 'Grid view'
      }).eachPage(async (records, fetchNextPage) => {
        console.log(`📄 Processing ${records.length} records...`);
        
        for (const record of records) {
          totalRecords++;
          const success = await insertRecord(record, fieldTypes);
          if (success) {
            successfulRecords++;
          }
          
          if (totalRecords % 10 === 0) {
            console.log(`📈 Progress: ${successfulRecords}/${totalRecords} records migrated`);
          }
        }
        
        fetchNextPage();
      }, (err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
    
    console.log(`🎉 Migration complete! ${successfulRecords}/${totalRecords} records migrated successfully.`);
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
  }
}

// Run the migration
migrateData().catch(err => {
  console.error("💥 Critical error:", err);
});
