const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const csv = require('csv-parser');
require('dotenv').config();

const SUPABASE_URL = 'https://rnpxnaqfoqdivxrlozfr.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJucHhuYXFmb3FkaXZ4cmxvemZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc1NzEzMywiZXhwIjoyMDY0MzMzMTMzfQ.s8dcBd1Yg45uKrOqR8mH-RzD7hQxqlPCwKds366RszI';

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

class FreshAirtableImporter {
  constructor() {
    this.stats = {
      totalRecords: 0,
      updated: 0,
      errors: 0,
      freshUrls: 0
    };
  }

  /**
   * Import fresh Airtable data from CSV export
   */
  async importFromCSV(csvFilePath) {
    console.log('📥 Importing Fresh Airtable Data');
    console.log('================================');
    console.log(`📄 Source: ${csvFilePath}`);
    console.log('');

    if (!fs.existsSync(csvFilePath)) {
      console.error('❌ CSV file not found!');
      console.log('');
      console.log('📋 Please:');
      console.log('1. Export fresh data from Airtable as CSV');
      console.log('2. Save the file in this directory');
      console.log('3. Run this script with the correct filename');
      return false;
    }

    const records = [];
    
    return new Promise((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          records.push(row);
        })
        .on('end', async () => {
          console.log(`📊 Found ${records.length} records in CSV`);
          this.stats.totalRecords = records.length;
          
          if (records.length === 0) {
            console.log('❌ No records found in CSV file');
            resolve(false);
            return;
          }

          // Process records
          await this.processRecords(records);
          resolve(true);
        })
        .on('error', (error) => {
          console.error('❌ CSV parsing error:', error.message);
          reject(error);
        });
    });
  }

  /**
   * Process and update records with fresh URLs
   */
  async processRecords(records) {
    console.log('');
    console.log('🔄 Processing records...');
    console.log('');

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      await this.updateRecord(record, i + 1);

      // Progress update every 50 records
      if ((i + 1) % 50 === 0) {
        console.log('');
        console.log(`📈 Progress: ${i + 1}/${records.length} (${Math.round((i + 1)/records.length*100)}%)`);
        console.log(`   ✅ Updated: ${this.stats.updated}`);
        console.log(`   🔗 Fresh URLs: ${this.stats.freshUrls}`);
        console.log(`   ❌ Errors: ${this.stats.errors}`);
        console.log('');
      }

      // Small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Final results
    console.log('');
    console.log('🎉 IMPORT COMPLETE!');
    console.log('==================');
    console.log(`📊 Total records: ${this.stats.totalRecords}`);
    console.log(`✅ Successfully updated: ${this.stats.updated}`);
    console.log(`🔗 Fresh URLs found: ${this.stats.freshUrls}`);
    console.log(`❌ Errors: ${this.stats.errors}`);
    console.log('');
    
    if (this.stats.freshUrls > 0) {
      console.log('🚀 Ready for Google Drive migration!');
      console.log('📋 Next step: node migrate-pdfs-service-account.js');
    } else {
      console.log('⚠️  No fresh URLs found - check Airtable export');
    }
  }

  /**
   * Update single record with fresh data
   */
  async updateRecord(record, index) {
    try {
      // Extract key fields (adjust field names based on your CSV structure)
      const name = record['Name'] || record['name'] || record['Case Study Name'];
      const company = record['Company'] || record['company'];
      const pdfField = record['PDF'] || record['pdf'] || record['Attachment'];
      
      if (!name) {
        console.log(`⏭️  ${index}: Skipping - no name found`);
        return;
      }

      console.log(`📄 ${index}/${this.stats.totalRecords}: ${name} (${company || 'Unknown'})`);

      // Check if PDF field has fresh URL
      if (!pdfField || !pdfField.includes('airtableusercontent.com')) {
        console.log(`   ⏭️  No PDF attachment found`);
        return;
      }

      // Parse PDF data (Airtable exports attachments as JSON-like strings)
      let pdfData;
      try {
        // Handle different Airtable export formats
        if (pdfField.startsWith('[')) {
          pdfData = JSON.parse(pdfField);
        } else if (pdfField.includes('airtableusercontent.com')) {
          // Direct URL format
          pdfData = [{
            url: pdfField.split(',')[0].trim(), // Take first URL if multiple
            filename: 'document.pdf'
          }];
        } else {
          throw new Error('Unknown PDF format');
        }
      } catch (parseError) {
        console.log(`   ❌ Could not parse PDF data: ${parseError.message}`);
        this.stats.errors++;
        return;
      }

      if (!pdfData || pdfData.length === 0 || !pdfData[0].url) {
        console.log(`   ⏭️  No valid PDF URL found`);
        return;
      }

      const freshUrl = pdfData[0].url;
      
      // Check if URL is fresh (not expired)
      const urlTimestamp = this.extractTimestampFromUrl(freshUrl);
      const now = Date.now();
      const urlAge = now - urlTimestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (urlAge > maxAge) {
        console.log(`   ⚠️  URL may be expired (${Math.round(urlAge / (60 * 60 * 1000))} hours old)`);
      } else {
        console.log(`   ✅ Fresh URL (${Math.round(urlAge / (60 * 60 * 1000))} hours old)`);
        this.stats.freshUrls++;
      }

      // Find matching record in database
      const { data: existingRecords, error: searchError } = await supabase
        .from('airtable_data')
        .select('id, name, company')
        .ilike('name', `%${name}%`)
        .limit(5);

      if (searchError) {
        console.log(`   ❌ Database search error: ${searchError.message}`);
        this.stats.errors++;
        return;
      }

      if (!existingRecords || existingRecords.length === 0) {
        console.log(`   ⏭️  No matching record found in database`);
        return;
      }

      // Find best match
      let bestMatch = existingRecords[0];
      if (existingRecords.length > 1) {
        // Try to find exact match by company too
        const exactMatch = existingRecords.find(r => 
          r.company && company && 
          r.company.toLowerCase().includes(company.toLowerCase())
        );
        if (exactMatch) {
          bestMatch = exactMatch;
        }
      }

      // Update record with fresh URL and PDF data
      const { error: updateError } = await supabase
        .from('airtable_data')
        .update({
          pdf_url: freshUrl,
          pdf: JSON.stringify(pdfData)
        })
        .eq('id', bestMatch.id);

      if (updateError) {
        console.log(`   ❌ Update failed: ${updateError.message}`);
        this.stats.errors++;
        return;
      }

      this.stats.updated++;
      console.log(`   ✅ Updated with fresh URL`);

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      this.stats.errors++;
    }
  }

  /**
   * Extract timestamp from Airtable URL to check freshness
   */
  extractTimestampFromUrl(url) {
    try {
      // Airtable URLs contain timestamps like /1751839200000/
      const timestampMatch = url.match(/\/(\d{13})\//);
      if (timestampMatch) {
        return parseInt(timestampMatch[1]);
      }
      return Date.now(); // Default to current time if can't extract
    } catch (error) {
      return Date.now();
    }
  }

  /**
   * Import from JSON export (alternative format)
   */
  async importFromJSON(jsonFilePath) {
    console.log('📥 Importing Fresh Airtable Data (JSON)');
    console.log('=======================================');
    console.log(`📄 Source: ${jsonFilePath}`);
    console.log('');

    if (!fs.existsSync(jsonFilePath)) {
      console.error('❌ JSON file not found!');
      return false;
    }

    try {
      const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
      const records = Array.isArray(jsonData) ? jsonData : jsonData.records || [];
      
      console.log(`📊 Found ${records.length} records in JSON`);
      this.stats.totalRecords = records.length;
      
      if (records.length === 0) {
        console.log('❌ No records found in JSON file');
        return false;
      }

      await this.processRecords(records);
      return true;
    } catch (error) {
      console.error('❌ JSON parsing error:', error.message);
      return false;
    }
  }
}

async function main() {
  const importer = new FreshAirtableImporter();
  
  // Check for CSV or JSON files
  const csvFiles = ['airtable-export.csv', 'fresh-data.csv', 'case-studies.csv'];
  const jsonFiles = ['airtable-export.json', 'fresh-data.json', 'case-studies.json'];
  
  let importFile = null;
  let importType = null;
  
  // Look for CSV files first
  for (const file of csvFiles) {
    if (fs.existsSync(file)) {
      importFile = file;
      importType = 'csv';
      break;
    }
  }
  
  // If no CSV, look for JSON
  if (!importFile) {
    for (const file of jsonFiles) {
      if (fs.existsSync(file)) {
        importFile = file;
        importType = 'json';
        break;
      }
    }
  }
  
  if (!importFile) {
    console.log('❌ No Airtable export file found!');
    console.log('');
    console.log('📋 Please export fresh data from Airtable and save as one of:');
    console.log('   CSV files: airtable-export.csv, fresh-data.csv, case-studies.csv');
    console.log('   JSON files: airtable-export.json, fresh-data.json, case-studies.json');
    console.log('');
    console.log('💡 Make sure the export includes:');
    console.log('   - Name/Case Study Name field');
    console.log('   - Company field');
    console.log('   - PDF/Attachment field with fresh URLs');
    return;
  }
  
  console.log(`📁 Found export file: ${importFile} (${importType.toUpperCase()})`);
  console.log('');
  
  let success = false;
  if (importType === 'csv') {
    success = await importer.importFromCSV(importFile);
  } else {
    success = await importer.importFromJSON(importFile);
  }
  
  if (success && importer.stats.freshUrls > 0) {
    console.log('');
    console.log('🚀 Ready to proceed with Google Drive migration!');
    console.log('📋 Run: node migrate-pdfs-service-account.js');
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error('💥 Import failed:', err.message);
    process.exit(1);
  });
}

module.exports = FreshAirtableImporter;
